/// Route constants for the application
/// Centralized route definitions for type-safe navigation
class AppRoutes {
  // Private constructor to prevent instantiation
  AppRoutes._();

  // Auth Routes
  static const String login = '/login';
  static const String selectAccountType = '/select-account-type';
  static const String personalInfoConfirmation = '/personal-info-confirmation';
  static const String ctvPolicy = '/ctv-policy';
  static const String kycIdGuide = '/kyc-id-guide';
  static const String registrationSuccess = '/registration-success';

  // Main App Routes
  static const String home = '/home';

  // Document Routes
  static const String captureDocument = '/capture-document';
  static const String previewDocument = '/preview-document';

  // Identity Routes
  static const String identityUpload = '/identity-upload';
  static const String qrScan = '/qr-scan';

  // Demo Routes
  static const String navigationDemo = '/navigation-demo';

  // Route Names (for named navigation)
  static const String loginName = 'login';
  static const String selectAccountTypeName = 'selectAccountType';
  static const String personalInfoConfirmationName = 'personalInfoConfirmation';
  static const String ctvPolicyName = 'ctvPolicy';
  static const String kycIdGuideName = 'kycIdGuide';
  static const String registrationSuccessName = 'registrationSuccess';
  static const String homeName = 'home';
  static const String captureDocumentName = 'captureDocument';
  static const String previewDocumentName = 'previewDocument';
  static const String identityUploadName = 'identityUpload';
  static const String navigationDemoName = 'navigationDemo';
  static const String qrScanName = 'qrScan';

  /// Get all route paths as a list
  static List<String> get allRoutes => [
        login,
        selectAccountType,
        personalInfoConfirmation,
        ctvPolicy,
        kycIdGuide,
        registrationSuccess,
        home,
        captureDocument,
        previewDocument,
        identityUpload,
        navigationDemo,
        qrScan,
      ];

  /// Check if a route path is valid
  static bool isValidRoute(String path) {
    return allRoutes.contains(path);
  }
}
