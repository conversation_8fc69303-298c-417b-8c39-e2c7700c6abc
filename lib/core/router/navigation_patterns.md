# Navigation Patterns Guide

> **📚 Comprehensive Guide**: <PERSON>em chi tiết đ<PERSON>y đủ tại [docs/SYSTEMS_GUIDE.md](../../docs/SYSTEMS_GUIDE.md#-navigation-system)

## 🎯 Overview

This guide explains when to use different navigation methods in Go Router to maintain proper navigation stack and back button behavior.

## 📚 Navigation Methods

### 1. `context.push()` - Add to Stack
**Use when:** User should be able to go back to previous screen

```dart
// ✅ Good: User can go back
context.goToSelectAccountType();    // Uses push()
context.goToCtvPolicy();            // Uses push()
context.goToKycIdGuide();           // Uses push()
```

**Behavior:**
- Adds new route to navigation stack
- Shows back button automatically
- Hardware back button works
- Can use `context.pop()` to go back

### 2. `context.go()` - Replace Current Route
**Use when:** User should NOT go back to previous screen

```dart
// ✅ Good: Clear login from stack after successful login
context.replaceWithHome();          // Uses go()

// ✅ Good: Logout - clear everything and go to login
context.replaceWithLogin();         // Uses go()
```

**Behavior:**
- Replaces current route in stack
- No back button shown
- Hardware back button exits app (if no other routes in stack)
- Cannot go back to replaced route

## 🔄 Navigation Flow Examples

### Auth Flow (Recommended Pattern)
```dart
// Login Screen
void _handleLogin() {
  // Replace login with home - user shouldn't go back to login
  context.replaceWithHome();         // ✅ Uses go()
}

// Register button in login
void _goToRegister() {
  // Push to register - user can go back to login
  context.goToSelectAccountType();   // ✅ Uses push()
}
```

### Document Flow
```dart
// From any screen to capture document
void _captureDocument() {
  // Push to capture - user can go back
  context.goToCaptureDocument();     // ✅ Uses push()
}

// From capture to preview
void _previewDocument() {
  // Push to preview - user can go back to capture
  context.pushPreviewDocumentWithParams(...); // ✅ Uses push()
}
```

### Registration Flow
```dart
// Select Account Type → CTV Policy
context.goToCtvPolicy();            // ✅ Uses push()

// CTV Policy → KYC Guide  
context.goToKycIdGuide();           // ✅ Uses push()

// KYC Guide → Identity Upload
context.goToIdentityUpload();       // ✅ Uses push()

// Identity Upload → Personal Info
context.goToPersonalInfoConfirmation(); // ✅ Uses push()

// Personal Info → Success (final step)
context.goToRegistrationSuccess();  // ✅ Uses push()
```

## 🛠️ Available Methods

### Push Methods (Add to Stack)
```dart
// Auth Flow
context.goToSelectAccountType();
context.goToCtvPolicy();
context.goToKycIdGuide();
context.goToIdentityUpload();
context.goToPersonalInfoConfirmation();
context.goToRegistrationSuccess();

// Document Flow
context.goToCaptureDocument();
context.goToPreviewDocument();

// General
context.pushToHome();               // Push home (not replace)
```

### Replace Methods (Clear Stack)
```dart
// Login/Logout
context.replaceWithLogin();         // For logout
context.replaceWithHome();          // For successful login
context.goToLogin();                // For initial app start

// General
context.replaceWith('/any-route');
context.clearAndGoTo('/any-route');
```

### Utility Methods
```dart
// Go back
context.goBack();                   // Smart back with fallback

// Check if can go back
if (context.canPop()) {
  context.pop();
}
```

## 🎯 Best Practices

### 1. Login Flow
```dart
// ✅ CORRECT
void _handleLogin() {
  context.replaceWithHome();        // Clear login from stack
}

// ❌ WRONG
void _handleLogin() {
  context.pushToHome();             // User can go back to login
}
```

### 2. Registration Flow
```dart
// ✅ CORRECT - Each step can go back
SelectAccountType → CtvPolicy → KycGuide → IdentityUpload → PersonalInfo → Success
     push()           push()      push()        push()         push()
```

### 3. Document Flow
```dart
// ✅ CORRECT - Can go back to previous step
Home → CaptureDocument → PreviewDocument
      push()            push()
```

### 4. Logout Flow
```dart
// ✅ CORRECT
void _handleLogout() {
  context.replaceWithLogin();       // Clear all screens, go to login
}
```

## 🐛 Common Issues & Solutions

### Issue: No Back Button
**Problem:** Using `context.go()` when should use `context.push()`
```dart
// ❌ WRONG
context.goToSelectAccountType();   // If this uses go()

// ✅ FIX
context.goToSelectAccountType();   // Should use push() internally
```

### Issue: Hardware Back Exits App
**Problem:** Navigation stack is empty
**Solution:** Ensure proper use of push() vs go()

### Issue: Can Go Back to Login After Login
**Problem:** Using push() for login flow
```dart
// ❌ WRONG
context.pushToHome();              // After login

// ✅ FIX  
context.replaceWithHome();         // After login
```

## 📱 Testing Navigation

### Test Back Button Behavior
1. Navigate through flow
2. Check back button appears when expected
3. Test hardware back button
4. Verify stack behavior

### Test Login Flow
1. Login should replace with home
2. No back button on home after login
3. Hardware back should exit app from home

### Test Registration Flow
1. Each step should have back button
2. Can navigate back through all steps
3. Final success screen should have back button
