/// Environment configuration constants
/// Centralized place for all environment-specific URLs and settings
class EnvironmentConstants {
  
  // ✅ Base URLs for different environments
  static const String devBaseUrl = 'https://dev-api.kienlongbank.com';
  static const String stagingBaseUrl = 'https://staging-api.kienlongbank.com';
  static const String prodBaseUrl = 'https://api.kienlongbank.com';
  
  // ✅ API configuration
  static const String apiVersion = 'v1';
  static const int defaultTimeoutDuration = 30000; // 30 seconds
  
  // ✅ Environment-specific settings
  static const bool devLoggingEnabled = true;
  static const bool stagingLoggingEnabled = true;
  static const bool prodLoggingEnabled = false;
  
  static const bool devMockApiEnabled = true;
  static const bool stagingMockApiEnabled = false;
  static const bool prodMockApiEnabled = false;
  
  // ✅ Additional environment-specific configurations
  static const int devTimeoutDuration = 30000;
  static const int stagingTimeoutDuration = 30000;
  static const int prodTimeoutDuration = 30000;
  
  // ✅ Feature flags per environment (for future use)
  static const Map<String, bool> devFeatureFlags = {
    'debug_menu': true,
    'mock_payments': true,
    'environment_selector': true,
    'crash_reporting': false,
  };
  
  static const Map<String, bool> stagingFeatureFlags = {
    'debug_menu': true,
    'mock_payments': false,
    'environment_selector': true,
    'crash_reporting': true,
  };
  
  static const Map<String, bool> prodFeatureFlags = {
    'debug_menu': false,
    'mock_payments': false,
    'environment_selector': false,
    'crash_reporting': true,
  };
  
  // ✅ Helper methods to get full API URLs
  static String getFullApiUrl(String baseUrl) {
    return '$baseUrl/api/$apiVersion';
  }
  
  static String get devFullApiUrl => getFullApiUrl(devBaseUrl);
  static String get stagingFullApiUrl => getFullApiUrl(stagingBaseUrl);
  static String get prodFullApiUrl => getFullApiUrl(prodBaseUrl);
  
  // ✅ Validation helpers
  static bool isValidEnvironmentUrl(String url) {
    return url.startsWith('https://') && url.contains('kienlongbank.com');
  }
  
  // ✅ Environment detection helpers (for future use)
  static bool isDevUrl(String url) => url.contains('dev-api');
  static bool isStagingUrl(String url) => url.contains('staging-api');
  static bool isProdUrl(String url) => url == prodBaseUrl;
}
