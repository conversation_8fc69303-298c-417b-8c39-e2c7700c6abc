/// API endpoint paths constants
/// Centralized location for all API paths to avoid hardcoding
class ApiPaths {
  // Private constructor to prevent instantiation
  ApiPaths._();

  /// Authentication endpoints
  static const String authLogin = '/auth/login';
  static const String authLogout = '/auth/logout';
  static const String authRegister = '/auth/register';
  static const String authRefresh = '/auth/refresh';
  static const String authForgotPassword = '/auth/forgot-password';
  static const String authResetPassword = '/auth/reset-password';

  /// User endpoints
  static const String userProfile = '/profile';
  static const String users = '/users';
  static const String userSettings = '/user/settings';
  static const String userAvatar = '/user/avatar';

  /// Product endpoints
  static const String products = '/products';
  static const String categories = '/categories';
  static const String productDetail = '/product'; // Use with /{id}
  static const String productSearch = '/product/search';

  /// Order endpoints
  static const String orders = '/orders';
  static const String orderDetail = '/order'; // Use with /{id}
  static const String orderCancel = '/order/cancel';
  static const String orderHistory = '/orders/history';

  /// Payment endpoints
  static const String payments = '/payments';
  static const String paymentMethods = '/payment/methods';
  static const String paymentProcess = '/payment/process';

  /// Notification endpoints
  static const String notifications = '/notifications';
  static const String notificationRead = '/notification/read';
  static const String notificationSettings = '/notification/settings';

  /// Helper methods to check path patterns
  static bool isAuthPath(String path) {
    return path.startsWith('/auth/');
  }

  static bool isUserPath(String path) {
    return path.startsWith('/users') || 
           path.startsWith('/profile') || 
           path.startsWith('/user/');
  }

  static bool isProductPath(String path) {
    return path.startsWith('/products') || 
           path.startsWith('/categories') || 
           path.startsWith('/product/');
  }

  static bool isOrderPath(String path) {
    return path.startsWith('/orders') || 
           path.startsWith('/order/');
  }

  static bool isPaymentPath(String path) {
    return path.startsWith('/payments') || 
           path.startsWith('/payment/');
  }

  static bool isNotificationPath(String path) {
    return path.startsWith('/notifications') || 
           path.startsWith('/notification/');
  }

  /// Get all auth paths for documentation/testing
  static List<String> get authPaths => [
    authLogin,
    authLogout,
    authRegister,
    authRefresh,
    authForgotPassword,
    authResetPassword,
  ];

  /// Get all user paths for documentation/testing
  static List<String> get userPaths => [
    userProfile,
    users,
    userSettings,
    userAvatar,
  ];

  /// Get all product paths for documentation/testing
  static List<String> get productPaths => [
    products,
    categories,
    productDetail,
    productSearch,
  ];
}
