import 'package:sales_app/di/injection.dart';
import 'package:sales_app/domain/services/logger_service.dart';

/// Helper class để dễ dàng sử dụng logging trong toàn bộ ứng dụng
/// 
/// ## C<PERSON>ch sử dụng:
/// 
/// ```dart
/// // Log thông tin chung
/// AppLogger.info('Ứng dụng khởi tạo thành công');
/// 
/// // Log với data đi kèm
/// AppLogger.info('User login', data: {'userId': '123', 'email': '<EMAIL>'});
/// 
/// // Log debug (chỉ hiển thị trong debug mode)
/// AppLogger.debug('Giá trị biến', data: {'count': 10});
/// 
/// // Log cảnh báo
/// AppLogger.warning('Kết nối mạng không ổn định');
/// 
/// // Log lỗi
/// AppLogger.error('<PERSON><PERSON>ng nhập thất bại', error: exception, stackTrace: stackTrace);
/// 
/// // Log sự kiện người dùng
/// AppLogger.event('Button clicked', data: {'buttonName': 'login'});
/// 
/// // Log network request/response
/// AppLogger.networkRequest('POST', '/api/login', body: {'email': '<EMAIL>'});
/// AppLogger.networkResponse('POST', '/api/login', 200, duration: Duration(milliseconds: 500));
/// ```
/// 
/// ## Lưu ý:
/// - Trong production mode, tất cả log sẽ bị tắt
/// - Trong debug mode, log sẽ hiển thị với màu sắc và format đẹp
/// - Sử dụng `AppLogger` thay vì inject `LoggerService` trực tiếp để code ngắn gọn hơn
///
/// ## Tài liệu chi tiết:
/// Xem thêm tại: [docs/SYSTEMS_GUIDE.md](../../docs/SYSTEMS_GUIDE.md#-logging-system)
class AppLogger {
  static LoggerService get _logger => getIt<LoggerService>();

  /// Log thông tin chung
  static void info(String message, {Map<String, dynamic>? data}) {
    _logger.logInfo(message, data: data);
  }

  /// Log cho quá trình debug
  static void debug(String message, {Map<String, dynamic>? data}) {
    _logger.logDebug(message, data: data);
  }

  /// Log cảnh báo
  static void warning(String message, {Map<String, dynamic>? data}) {
    _logger.logWarning(message, data: data);
  }

  /// Log lỗi nghiêm trọng
  static void error(
    String message, {
    Object? error,
    StackTrace? stackTrace,
    Map<String, dynamic>? data,
  }) {
    _logger.logError(
      message,
      error: error,
      stackTrace: stackTrace,
      data: data,
    );
  }

  /// Log sự kiện (user action, API call, etc.)
  static void event(String event, {Map<String, dynamic>? data}) {
    _logger.logEvent(event, data: data);
  }

  /// Log network request
  static void networkRequest(
    String method,
    String url, {
    Map<String, dynamic>? headers,
    Map<String, dynamic>? body,
  }) {
    _logger.logNetworkRequest(
      method,
      url,
      headers: headers,
      body: body,
    );
  }

  /// Log network response
  static void networkResponse(
    String method,
    String url,
    int statusCode, {
    Map<String, dynamic>? response,
    Duration? duration,
  }) {
    _logger.logNetworkResponse(
      method,
      url,
      statusCode,
      response: response,
      duration: duration,
    );
  }
} 