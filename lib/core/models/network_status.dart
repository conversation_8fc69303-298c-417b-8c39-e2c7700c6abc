import 'package:freezed_annotation/freezed_annotation.dart';

part 'network_status.freezed.dart';

/// Core network status model
/// Shared across all layers - can be used directly without mapping
@freezed
class NetworkStatus with _$NetworkStatus {
  /// Connected to internet
  const factory NetworkStatus.connected() = _Connected;
  
  /// Disconnected from internet
  const factory NetworkStatus.disconnected() = _Disconnected;
  
  /// Unstable connection (frequent disconnections)
  const factory NetworkStatus.unstable() = _Unstable;
  
  /// Checking connection status
  const factory NetworkStatus.checking() = _Checking;
}

/// Extension methods for NetworkStatus
extension NetworkStatusExtension on NetworkStatus {
  /// Check if currently connected
  bool get isConnected => this is _Connected;
  
  /// Check if currently disconnected
  bool get isDisconnected => this is _Disconnected;
  
  /// Check if connection is unstable
  bool get isUnstable => this is _Unstable;
  
  /// Check if checking connection
  bool get isChecking => this is _Checking;
  
  /// Get status icon (language-independent)
  String get statusIcon {
    return when(
      connected: () => '✅',
      disconnected: () => '❌',
      unstable: () => '⚠️',
      checking: () => '🔄',
    );
  }
}
