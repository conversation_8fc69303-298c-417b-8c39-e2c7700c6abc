import 'dart:async';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:sales_app/di/injection.dart';
import 'package:sales_app/core/utils/app_logger.dart';
import 'package:sales_app/domain/services/network_info.dart';
import 'network_state.dart';

part 'network_monitor_controller.g.dart';

/// Global network monitoring controller
/// Tracks network connectivity and provides real-time status updates
@riverpod
class NetworkMonitorController extends _$NetworkMonitorController {
  late final NetworkInfo _networkInfo;
  StreamSubscription<bool>? _connectivitySubscription;
  Timer? _unstableTimer;
  
  // Track connection stability
  int _disconnectionCount = 0;
  DateTime? _lastDisconnection;
  static const int _unstableThreshold = 3; // 3 disconnections in 2 minutes = unstable
  static const Duration _unstableWindow = Duration(minutes: 2);
  static const Duration _checkDelay = Duration(seconds: 2);

  @override
  NetworkState build() {
    _networkInfo = getIt<NetworkInfo>();
    _initializeNetworkMonitoring();
    return const NetworkState.checking();
  }

  /// Initialize network monitoring
  void _initializeNetworkMonitoring() {
    AppLogger.info('Initializing network monitoring');
    
    // Check initial connection status
    _checkInitialConnection();
    
    // Listen to connectivity changes
    _startConnectivityListener();
  }

  /// Check initial connection status
  Future<void> _checkInitialConnection() async {
    try {
      final isConnected = await _networkInfo.isConnected;
      state = isConnected
          ? const NetworkState.connected()
          : const NetworkState.disconnected();

      AppLogger.info('Initial network status', data: {
        'isConnected': isConnected,
        'state': state.toString(),
      });
    } catch (e) {
      AppLogger.error('Error checking initial connection', error: e);
      state = const NetworkState.disconnected();
    }
  }

  /// Start listening to connectivity changes
  void _startConnectivityListener() {
    _connectivitySubscription?.cancel();
    
    _connectivitySubscription = _networkInfo.connectivityStream.listen(
      (isConnected) {
        _handleConnectivityChange(isConnected);
      },
      onError: (error) {
        AppLogger.error('Connectivity stream error', error: error);
        state = const NetworkState.disconnected();
      },
    );
  }

  /// Handle connectivity changes
  void _handleConnectivityChange(bool isConnected) {
    AppLogger.info('Network connectivity changed', data: {
      'isConnected': isConnected,
      'previousState': state.toString(),
      'timestamp': DateTime.now().toIso8601String(),
    });

    if (isConnected) {
      _handleReconnection();
    } else {
      _handleDisconnection();
    }
  }

  /// Handle reconnection
  void _handleReconnection() {
    // Cancel unstable timer if running
    _unstableTimer?.cancel();
    
    // Check if this was part of unstable pattern
    if (_isConnectionUnstable()) {
      state = const NetworkState.unstable();
      AppLogger.warning('Connection marked as unstable', data: {
        'disconnectionCount': _disconnectionCount,
        'timeWindow': _unstableWindow.inMinutes,
      });
      
      // Clear unstable state after some time of stable connection
      _unstableTimer = Timer(const Duration(minutes: 5), () {
        _resetStabilityTracking();
        state = const NetworkState.connected();
        AppLogger.info('Connection stabilized, returning to normal state');
      });
    } else {
      state = const NetworkState.connected();
    }
  }

  /// Handle disconnection
  void _handleDisconnection() {
    state = const NetworkState.disconnected();
    _trackDisconnection();
  }

  /// Track disconnection for stability analysis
  void _trackDisconnection() {
    final now = DateTime.now();
    
    // Reset count if outside the unstable window
    if (_lastDisconnection != null && 
        now.difference(_lastDisconnection!) > _unstableWindow) {
      _disconnectionCount = 0;
    }
    
    _disconnectionCount++;
    _lastDisconnection = now;
    
    AppLogger.info('Disconnection tracked', data: {
      'count': _disconnectionCount,
      'timestamp': now.toIso8601String(),
    });
  }

  /// Check if connection is unstable
  bool _isConnectionUnstable() {
    return _disconnectionCount >= _unstableThreshold;
  }

  /// Reset stability tracking
  void _resetStabilityTracking() {
    _disconnectionCount = 0;
    _lastDisconnection = null;
    _unstableTimer?.cancel();
  }

  /// Manually check connection status
  Future<void> checkConnection() async {
    state = const NetworkState.checking();

    // Add small delay for better UX
    await Future.delayed(_checkDelay);

    try {
      final isConnected = await _networkInfo.isConnected;
      state = isConnected
          ? const NetworkState.connected()
          : const NetworkState.disconnected();

      AppLogger.info('Manual connection check completed', data: {
        'isConnected': isConnected,
      });
    } catch (e) {
      AppLogger.error('Error during manual connection check', error: e);
      state = const NetworkState.disconnected();
    }
  }

  /// Force reset to connected state (for testing)
  void forceConnected() {
    _resetStabilityTracking();
    state = const NetworkState.connected();
    AppLogger.info('Network state forced to connected');
  }

  /// Dispose resources
  void dispose() {
    AppLogger.info('Disposing network monitor controller');
    _connectivitySubscription?.cancel();
    _unstableTimer?.cancel();
  }
}
