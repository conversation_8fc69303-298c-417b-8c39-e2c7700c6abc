import 'dart:async';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:sales_app/di/injection.dart';
import 'package:sales_app/domain/services/network_monitoring_service.dart';
import 'package:sales_app/presentation/mappers/network_state_mapper.dart';
import 'network_state.dart';

part 'network_monitor_controller.g.dart';

/// Presentation layer controller for network monitoring
/// Delegates business logic to NetworkMonitoringService (domain layer)
/// Follows clean architecture by keeping only state management here
@riverpod
class NetworkMonitorController extends _$NetworkMonitorController {
  late final NetworkMonitoringService _networkMonitoringService;
  StreamSubscription<NetworkState>? _stateSubscription;

  @override
  NetworkState build() {
    _networkMonitoringService = getIt<NetworkMonitoringService>();
    _initializeNetworkMonitoring();
    return NetworkStateMapper.fromDomain(_networkMonitoringService.currentStatus);
  }

  /// Initialize network monitoring
  void _initializeNetworkMonitoring() {
    // Initialize domain service
    _networkMonitoringService.initialize();

    // Listen to state changes from domain service and map to presentation state
    _stateSubscription = NetworkStateMapper.mapDomainStream(
      _networkMonitoringService.connectionStatusStream,
    ).listen(
      (newState) {
        state = newState;
      },
    );
  }

  /// Manually check connection status
  Future<void> checkConnection() async {
    await _networkMonitoringService.checkConnection();
  }

  /// Force reset to connected state
  /// Used for:
  /// - Emergency bypass when user wants to continue despite network issues
  /// - Handling false positive disconnection detection
  /// - Transitioning to offline mode with cached data
  /// - Testing and debugging scenarios
  void forceConnected() {
    _networkMonitoringService.forceConnected();
  }

  /// Dispose resources
  void dispose() {
    _stateSubscription?.cancel();
    _networkMonitoringService.dispose();
  }
}
