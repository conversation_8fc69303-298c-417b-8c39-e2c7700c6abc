import 'dart:async';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:sales_app/core/enums/network_connection_status.dart';
import 'package:sales_app/di/injection.dart';
import 'package:sales_app/domain/services/network_monitoring_service.dart';

part 'network_monitor_controller.g.dart';

/// Presentation layer controller for network monitoring
/// Uses NetworkConnectionStatus directly - simple and clean
/// Delegates business logic to NetworkMonitoringService (domain layer)
@riverpod
class NetworkMonitorController extends _$NetworkMonitorController {
  late final NetworkMonitoringService _networkMonitoringService;
  StreamSubscription<NetworkConnectionStatus>? _stateSubscription;

  @override
  NetworkConnectionStatus build() {
    _networkMonitoringService = getIt<NetworkMonitoringService>();
    _initializeNetworkMonitoring();
    return _networkMonitoringService.currentStatus;
  }

  /// Initialize network monitoring
  void _initializeNetworkMonitoring() {
    // Initialize domain service
    _networkMonitoringService.initialize();

    // Listen to state changes from domain service directly
    _stateSubscription = _networkMonitoringService.connectionStatusStream.listen(
      (newStatus) {
        state = newStatus;
      },
    );
  }

  /// Manually check connection status
  Future<void> checkConnection() async {
    await _networkMonitoringService.checkConnection();
  }

  /// Force reset to connected state
  /// Used for:
  /// - Emergency bypass when user wants to continue despite network issues
  /// - Handling false positive disconnection detection
  /// - Transitioning to offline mode with cached data
  /// - Testing and debugging scenarios
  void forceConnected() {
    _networkMonitoringService.forceConnected();
  }

  /// Dispose resources
  void dispose() {
    _stateSubscription?.cancel();
    _networkMonitoringService.dispose();
  }
}
