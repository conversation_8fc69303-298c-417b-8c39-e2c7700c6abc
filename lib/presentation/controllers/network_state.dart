import 'package:freezed_annotation/freezed_annotation.dart';

part 'network_state.freezed.dart';

/// Network connectivity state
@freezed
class NetworkState with _$NetworkState {
  /// Connected to internet
  const factory NetworkState.connected() = _Connected;
  
  /// Disconnected from internet
  const factory NetworkState.disconnected() = _Disconnected;
  
  /// Unstable connection (frequent disconnections)
  const factory NetworkState.unstable() = _Unstable;
  
  /// Checking connection status
  const factory NetworkState.checking() = _Checking;
}

/// Extension methods for NetworkState
extension NetworkStateExtension on NetworkState {
  /// Check if currently connected
  bool get isConnected => this is _Connected;
  
  /// Check if currently disconnected
  bool get isDisconnected => this is _Disconnected;
  
  /// Check if connection is unstable
  bool get isUnstable => this is _Unstable;
  
  /// Check if checking connection
  bool get isChecking => this is _Checking;
  
  // Note: Status messages are handled by NetworkStateLocalizer in presentation layer
  // This keeps domain layer free from UI concerns and localization dependencies
  
  /// Get status icon
  String get statusIcon {
    return when(
      connected: () => '✅',
      disconnected: () => '❌',
      unstable: () => '⚠️',
      checking: () => '🔄',
    );
  }
}
