// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'network_monitor_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$networkMonitorControllerHash() =>
    r'20bf21431f844264ad262ae6569c601fb42c6ef4';

/// Presentation layer controller for network monitoring
/// Delegates business logic to NetworkMonitoringService (domain layer)
/// Follows clean architecture by keeping only state management here
///
/// Copied from [NetworkMonitorController].
@ProviderFor(NetworkMonitorController)
final networkMonitorControllerProvider = AutoDisposeNotifierProvider<
    NetworkMonitorController, NetworkState>.internal(
  NetworkMonitorController.new,
  name: r'networkMonitorControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$networkMonitorControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$NetworkMonitorController = AutoDisposeNotifier<NetworkState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
