// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'network_monitor_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$networkMonitorControllerHash() =>
    r'3cf3babb90e6e19997625fbef89002e92e67f866';

/// Presentation layer controller for network monitoring
/// Uses NetworkConnectionStatus directly - simple and clean
/// Delegates business logic to NetworkMonitoringService (domain layer)
///
/// Copied from [NetworkMonitorController].
@ProviderFor(NetworkMonitorController)
final networkMonitorControllerProvider = AutoDisposeNotifierProvider<
    NetworkMonitorController, NetworkConnectionStatus>.internal(
  NetworkMonitorController.new,
  name: r'networkMonitorControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$networkMonitorControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$NetworkMonitorController
    = AutoDisposeNotifier<NetworkConnectionStatus>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
