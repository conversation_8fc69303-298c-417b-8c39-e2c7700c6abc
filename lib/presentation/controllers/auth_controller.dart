import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:sales_app/core/error/error_types.dart';
import 'package:sales_app/core/error/failures.dart';
import 'package:sales_app/core/utils/app_logger.dart';
import 'package:sales_app/domain/repositories/auth_repository.dart';
import 'package:sales_app/domain/services/network_info.dart';
import 'package:sales_app/domain/entities/user.dart';
import 'package:sales_app/presentation/controllers/auth_state.dart';
import 'package:sales_app/di/injection.dart';

part 'auth_controller.g.dart';

@riverpod
class AuthController extends _$AuthController {
  late final AuthRepository _authRepository;
  late final NetworkInfo _networkInfo;

  @override
  AuthState build() {
    _authRepository = getIt<AuthRepository>();
    _networkInfo = getIt<NetworkInfo>();

    // Check initial login status
    _checkInitialLoginStatus();

    return const AuthState.initial();
  }

  /// Check if user is already logged in
  Future<void> _checkInitialLoginStatus() async {
    try {
      final isLoggedIn = await _authRepository.isLoggedIn();
      if (isLoggedIn) {
        // Get user info if available
        // For now, we'll just set authenticated state
        state = const AuthState.authenticated(User(
          id: 'current_user',
          email: '<EMAIL>',
          name: 'Current User',
        ));
      } else {
        state = const AuthState.unauthenticated();
      }
    } catch (e) {
      AppLogger.error('Error checking login status', error: e);
      state = const AuthState.unauthenticated();
    }
  }

  /// Login with email and password
  Future<void> login({
    required String email,
    required String password,
  }) async {
    // Validate inputs
    if (!_isValidEmail(email)) {
      state = const AuthState.error(ValidationFailure(ValidationErrorType.invalidEmail));
      return;
    }

    if (!_isValidPassword(password)) {
      state = const AuthState.error(ValidationFailure(ValidationErrorType.invalidPassword));
      return;
    }

    // Check network connectivity
    if (!await _networkInfo.isConnected) {
      state = const AuthState.error(NetworkFailure(NetworkErrorType.noConnection));
      return;
    }

    // Start loading
    state = const AuthState.loading();

    try {
      AppLogger.info('Attempting login', data: {
        'email': email,
        'timestamp': DateTime.now().toIso8601String(),
      });

      final result = await _authRepository.login(
        email: email,
        password: password,
      );

      result.fold(
        (failure) {
          AppLogger.error('Login failed', error: failure.toString());
          state = AuthState.error(failure);
        },
        (user) {
          AppLogger.info('Login successful', data: {
            'userId': user.id,
            'email': user.email,
          });
          state = AuthState.authenticated(user);
        },
      );
    } catch (e) {
      AppLogger.error('Unexpected login error', error: e);
      state = const AuthState.error(ServerFailure(ServerErrorType.unknown));
    }
  }

  /// Logout user
  Future<void> logout() async {
    try {
      state = const AuthState.loading();

      await _authRepository.logout();

      AppLogger.info('User logged out successfully');
      state = const AuthState.unauthenticated();
    } catch (e) {
      AppLogger.error('Logout error', error: e);
      // Even if logout fails, clear local state
      state = const AuthState.unauthenticated();
    }
  }

  /// Validate email format
  bool _isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }

  /// Validate password strength
  bool _isValidPassword(String password) {
    return password.length >= 6;
  }

  /// Update email validation state
  void updateEmailValidation(String email) {
    _isValidEmail(email);
    // For now, we'll just store validation state internally
    // TODO: Implement proper state management for validation
  }

  /// Update password validation state
  void updatePasswordValidation(String password) {
    _isValidPassword(password);
    // For now, we'll just store validation state internally
    // TODO: Implement proper state management for validation
  }

  /// Clear error message
  void clearError() {
    // Clear error by setting to unauthenticated state
    if (state.hasError) {
      state = const AuthState.unauthenticated();
    }
  }

  /// Biometric login (placeholder)
  Future<void> biometricLogin() async {
    AppLogger.info('Biometric login attempted');
    // TODO: Implement biometric authentication
    state = const AuthState.error(ServerFailure(ServerErrorType.unknown));
  }
}
