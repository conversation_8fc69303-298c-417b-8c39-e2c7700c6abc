import 'package:flutter/material.dart';
import 'package:sales_app/core/error/error_codes.dart';
import 'package:sales_app/core/error/error_types.dart';
import 'package:sales_app/core/error/failures.dart';
import 'package:sales_app/generated/l10n.dart';

/// Maps error types to localized error messages with error codes
/// This class belongs to the presentation layer as it handles UI concerns (localization, context)
/// Format: message (error code)
///
/// Note: Network and server errors may have been retried automatically by SimpleRetryInterceptor
/// before reaching this mapper. The error messages reflect final failures after retry attempts.
class ErrorMessageMapper {
  /// Get localized error message from Failure with format: message (error code)
  static String getErrorMessage(BuildContext context, Failure failure) {
    final localizations = S.of(context);
    
    return failure.when(
      server: (errorType, serverMessage, serverErrorCode) => 
          _getServerErrorMessage(localizations, errorType, serverMessage, serverErrorCode),
      network: (errorType) => _getNetworkErrorMessage(localizations, errorType),
      cache: (errorType) => _getCacheErrorMessage(localizations, errorType),
      auth: (errorType, serverMessage, serverErrorCode) => 
          _getAuthErrorMessage(localizations, errorType, serverMessage, serverErrorCode),
      validation: (errorType, serverMessage, serverErrorCode) => 
          _getValidationErrorMessage(localizations, errorType, serverMessage, serverErrorCode),
    );
  }
  
  /// Map server error types to messages with format: message (error code)
  static String _getServerErrorMessage(S localizations, ServerErrorType errorType, String? serverMessage, String? serverErrorCode) {
    String message;
    String errorCode;
    
    // Use server message if available, otherwise use localized message
    switch (errorType) {
      case ServerErrorType.internalError:
        message = serverMessage ?? localizations.errorServerInternal;
        break;
      case ServerErrorType.notFound:
        message = serverMessage ?? localizations.errorServerNotFound;
        break;
      case ServerErrorType.tooManyRequests:
        message = serverMessage ?? localizations.errorServerTooManyRequests;
        break;
      case ServerErrorType.badRequest:
        message = serverMessage ?? localizations.errorServerBadRequest;
        break;
      case ServerErrorType.unknown:
        message = serverMessage ?? localizations.errorServerUnknown;
        break;
    }
    
    // Use server error code if available, otherwise use mapped error code
    errorCode = serverErrorCode ?? ErrorCodeMapper.getServerErrorCode(errorType);
    
    return '$message ($errorCode)';
  }
  
  /// Map network error types to messages with format: message (error code)
  /// Note: These errors may have been retried automatically before reaching here
  static String _getNetworkErrorMessage(S localizations, NetworkErrorType errorType) {
    String message;
    String errorCode = ErrorCodeMapper.getNetworkErrorCode(errorType);

    switch (errorType) {
      case NetworkErrorType.connectionTimeout:
        message = '${localizations.errorNetworkConnectionTimeout}\n💡 Kiểm tra kết nối mạng và thử lại';
        break;
      case NetworkErrorType.sendTimeout:
        message = '${localizations.errorNetworkSendTimeout}\n💡 Kết nối chậm, vui lòng thử lại';
        break;
      case NetworkErrorType.receiveTimeout:
        message = '${localizations.errorNetworkReceiveTimeout}\n💡 Máy chủ phản hồi chậm, thử lại sau';
        break;
      case NetworkErrorType.noConnection:
        message = '${localizations.errorNetworkNoConnection}\n💡 Bật WiFi hoặc dữ liệu di động';
        break;
      case NetworkErrorType.requestCancelled:
        message = localizations.errorNetworkRequestCancelled;
        break;
      case NetworkErrorType.certificateError:
        message = '${localizations.errorNetworkCertificate}\n💡 Kiểm tra cài đặt bảo mật';
        break;
      case NetworkErrorType.unknown:
        message = '${localizations.errorNetworkUnknown}\n💡 Kiểm tra kết nối và thử lại';
        break;
    }

    return '$message ($errorCode)';
  }
  
  /// Map cache error types to messages with format: message (error code)
  static String _getCacheErrorMessage(S localizations, CacheErrorType errorType) {
    String message;
    String errorCode = ErrorCodeMapper.getCacheErrorCode(errorType);
    
    switch (errorType) {
      case CacheErrorType.readError:
        message = localizations.errorCacheRead;
        break;
      case CacheErrorType.writeError:
        message = localizations.errorCacheWrite;
        break;
      case CacheErrorType.dataCorrupted:
        message = localizations.errorCacheDataCorrupted;
        break;
      case CacheErrorType.storageFull:
        message = localizations.errorCacheStorageFull;
        break;
      case CacheErrorType.unknown:
        message = localizations.errorCacheUnknown;
        break;
    }
    
    return '$message ($errorCode)';
  }

  /// Map auth error types to messages with format: message (error code)
  static String _getAuthErrorMessage(S localizations, AuthErrorType errorType, String? serverMessage, String? serverErrorCode) {
    String message;
    String errorCode;

    // Use server message if available, otherwise use localized message
    switch (errorType) {
      case AuthErrorType.invalidCredentials:
        message = serverMessage ?? localizations.errorAuthInvalidCredentials;
        break;
      case AuthErrorType.accessDenied:
        message = serverMessage ?? localizations.errorAuthAccessDenied;
        break;
      case AuthErrorType.tokenExpired:
        message = serverMessage ?? localizations.errorAuthTokenExpired;
        break;
      case AuthErrorType.userNotFound:
        message = serverMessage ?? localizations.errorAuthUserNotFound;
        break;
      case AuthErrorType.accountLocked:
        message = serverMessage ?? localizations.errorAuthAccountLocked;
        break;
      case AuthErrorType.unknown:
        message = serverMessage ?? localizations.errorAuthUnknown;
        break;
    }

    // Use server error code if available, otherwise use mapped error code
    errorCode = serverErrorCode ?? ErrorCodeMapper.getAuthErrorCode(errorType);

    return '$message ($errorCode)';
  }

  /// Map validation error types to messages with format: message (error code)
  static String _getValidationErrorMessage(S localizations, ValidationErrorType errorType, String? serverMessage, String? serverErrorCode) {
    String message;
    String errorCode;

    // Use server message if available, otherwise use localized message
    switch (errorType) {
      case ValidationErrorType.invalidEmail:
        message = serverMessage ?? localizations.errorValidationInvalidEmail;
        break;
      case ValidationErrorType.invalidPassword:
        message = serverMessage ?? localizations.errorValidationInvalidPassword;
        break;
      case ValidationErrorType.requiredField:
        message = serverMessage ?? localizations.errorValidationRequiredField;
        break;
      case ValidationErrorType.tooShort:
        message = serverMessage ?? localizations.errorValidationTooShort;
        break;
      case ValidationErrorType.tooLong:
        message = serverMessage ?? localizations.errorValidationTooLong;
        break;
      case ValidationErrorType.invalidFormat:
        message = serverMessage ?? localizations.errorValidationInvalidFormat;
        break;
      case ValidationErrorType.serverValidation:
        message = serverMessage ?? localizations.errorValidationServer;
        break;
      case ValidationErrorType.unknown:
        message = serverMessage ?? localizations.errorValidationUnknown;
        break;
    }

    // Use server error code if available, otherwise use mapped error code
    errorCode = serverErrorCode ?? ErrorCodeMapper.getValidationErrorCode(errorType);

    return '$message ($errorCode)';
  }
}
