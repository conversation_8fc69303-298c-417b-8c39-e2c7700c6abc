import 'package:flutter/material.dart';
import 'package:sales_app/generated/l10n.dart';
import 'package:sales_app/core/enums/network_connection_status.dart';
import 'package:sales_app/presentation/controllers/network_state.dart';

/// Helper class for localizing network state messages
/// Presentation layer utility for UI-specific localization
/// Follows clean architecture by keeping localization in presentation layer
class NetworkStateLocalizer {
  const NetworkStateLocalizer._();

  /// Get localized status message for network state
  static String getStatusMessage(BuildContext context, NetworkState state) {
    final localizations = S.of(context);
    
    return state.when(
      connected: () => localizations.networkConnected,
      disconnected: () => localizations.networkDisconnected,
      unstable: () => localizations.networkUnstable,
      checking: () => localizations.networkChecking,
    );
  }

  /// Get localized description for network state
  static String getDescription(BuildContext context, NetworkState state) {
    final localizations = S.of(context);
    
    return state.when(
      connected: () => '',
      disconnected: () => localizations.networkDisconnectedDescription,
      unstable: () => localizations.networkUnstableConnectionDescription,
      checking: () => localizations.networkCheckingConnectionDescription,
    );
  }

  /// Get localized retry button text based on state
  static String getRetryButtonText(BuildContext context, NetworkState state) {
    final localizations = S.of(context);
    
    return state.isChecking 
        ? localizations.networkRetryChecking 
        : localizations.networkRetry;
  }

  /// Get localized continue button text
  static String getContinueButtonText(BuildContext context) {
    return S.of(context).networkContinue;
  }

  /// Get localized reconnection success message
  static String getReconnectionMessage(BuildContext context) {
    return S.of(context).networkReconnectedSuccess;
  }

  /// Get localized unstable warning message
  static String getUnstableWarningMessage(BuildContext context) {
    return S.of(context).networkUnstableWarning;
  }

  /// Get localized unstable description message
  static String getUnstableDescriptionMessage(BuildContext context) {
    return S.of(context).networkUnstableDescription;
  }

  /// Get localized checking connection message
  static String getCheckingConnectionMessage(BuildContext context) {
    return S.of(context).networkCheckingConnection;
  }

  /// Get status icon (language-independent)
  static String getStatusIcon(NetworkState state) {
    return state.when(
      connected: () => '✅',
      disconnected: () => '❌',
      unstable: () => '⚠️',
      checking: () => '🔄',
    );
  }

  /// Get status icon from domain entity (bypassing presentation state)
  static String getStatusIconFromDomain(NetworkConnectionStatus domainStatus) {
    return domainStatus.statusIcon;
  }
}
