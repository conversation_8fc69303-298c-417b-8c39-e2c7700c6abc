import 'package:flutter/material.dart';
import '../../../core/theme/app_color.dart';
import '../../../core/constants/app_dimens.dart';

class LoadingOverlay extends StatelessWidget {
  final bool isLoading;
  final Widget child;
  final String? message;

  const LoadingOverlay({
    super.key,
    required this.isLoading,
    required this.child,
    this.message,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        child,
        if (isLoading)
          Container(
            color: const Color.fromRGBO(0, 0, 0, 0.5),
            child: Center(
              child: Container(
                padding: AppDimens.paddingAllLg,
                decoration: BoxDecoration(
                  color: AppColors.backgroundColor,
                  borderRadius: AppDimens.borderRadius16,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.2),
                      blurRadius: 10,
                      offset: const Offset(0, 5),
                    ),
                  ],
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation(AppColors.primaryColor),
                    ),
                    if (message != null) ...[
                      AppDimens.h16,
                      Text(
                        message!,
                        style: TextStyle(
                          fontSize: AppDimens.fontMD,
                          color: AppColors.textPrimary,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ],
                ),
              ),
            ),
          ),
      ],
    );
  }
}
