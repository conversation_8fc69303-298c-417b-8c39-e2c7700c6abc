import 'dart:io';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

/// Adaptive loading overlay that works on both iOS and Android
/// Can be used across different screens for consistent loading experience
class AdaptiveLoadingOverlay extends StatelessWidget {
  const AdaptiveLoadingOverlay({
    super.key,
    this.message,
    this.backgroundColor,
    this.opacity = 0.5,
    this.showMessage = true,
    this.messageStyle,
  });

  /// Loading message to display
  final String? message;
  
  /// Background color of the overlay
  final Color? backgroundColor;
  
  /// Opacity of the overlay background
  final double opacity;
  
  /// Whether to show the loading message
  final bool showMessage;
  
  /// Custom style for the loading message
  final TextStyle? messageStyle;

  @override
  Widget build(BuildContext context) {
    return Container(
      color: (backgroundColor ?? Colors.black).withValues(alpha: opacity),
      child: Center(
        child: Container(
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Adaptive loading indicator
              _buildAdaptiveLoadingIndicator(context),
              
              if (showMessage && message != null && message!.isNotEmpty) ...[
                const SizedBox(height: 16),
                Text(
                  message!,
                  style: messageStyle ?? 
                    Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Theme.of(context).colorScheme.onSurface,
                    ),
                  textAlign: TextAlign.center,
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAdaptiveLoadingIndicator(BuildContext context) {
    // Use platform-specific loading indicators
    if (Platform.isIOS) {
      return const CupertinoActivityIndicator(
        radius: 16,
      );
    } else {
      return CircularProgressIndicator(
        valueColor: AlwaysStoppedAnimation<Color>(
          Theme.of(context).colorScheme.primary,
        ),
      );
    }
  }
}

/// Extension to easily show/hide loading overlay
extension LoadingOverlayExtension on Widget {
  /// Wrap widget with loading overlay
  Widget withLoadingOverlay({
    required bool isLoading,
    String? message,
    Color? backgroundColor,
    double opacity = 0.5,
    bool showMessage = true,
    TextStyle? messageStyle,
  }) {
    return Stack(
      children: [
        this,
        if (isLoading)
          AdaptiveLoadingOverlay(
            message: message,
            backgroundColor: backgroundColor,
            opacity: opacity,
            showMessage: showMessage,
            messageStyle: messageStyle,
          ),
      ],
    );
  }
}

/// Predefined loading overlay configurations
class LoadingOverlayConfig {
  static const String defaultLoginMessage = 'Đang đăng nhập...';
  static const String defaultLogoutMessage = 'Đang đăng xuất...';
  static const String defaultLoadingMessage = 'Đang tải...';
  static const String defaultSavingMessage = 'Đang lưu...';
  static const String defaultDeletingMessage = 'Đang xóa...';
  
  /// Silent loading (no message) - DEFAULT for all types
  static AdaptiveLoadingOverlay silent() => const AdaptiveLoadingOverlay(
    showMessage: false,
  );
  
  /// Loading with custom message
  static AdaptiveLoadingOverlay withMessage(String message) => AdaptiveLoadingOverlay(
    message: message,
    showMessage: true,
  );
  
  // Legacy methods with messages (for backward compatibility)
  /// Login loading overlay with message
  static AdaptiveLoadingOverlay loginWithMessage() => const AdaptiveLoadingOverlay(
    message: defaultLoginMessage,
  );
  
  /// Logout loading overlay with message
  static AdaptiveLoadingOverlay logoutWithMessage() => const AdaptiveLoadingOverlay(
    message: defaultLogoutMessage,
  );
  
  /// Generic loading overlay with message
  static AdaptiveLoadingOverlay loadingWithMessage([String? message]) => AdaptiveLoadingOverlay(
    message: message ?? defaultLoadingMessage,
  );
  
  /// Saving data overlay with message
  static AdaptiveLoadingOverlay savingWithMessage() => const AdaptiveLoadingOverlay(
    message: defaultSavingMessage,
  );
  
  /// Deleting data overlay with message
  static AdaptiveLoadingOverlay deletingWithMessage() => const AdaptiveLoadingOverlay(
    message: defaultDeletingMessage,
  );
}
