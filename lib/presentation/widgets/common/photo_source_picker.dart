import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:sales_app/core/theme/app_color.dart';

/// Shows a cupertino-style action sheet for selecting photo source
/// 
/// Parameters:
/// - [context]: BuildContext for showing the modal
/// - [onTakePhoto]: Callback when user selects "Take Photo"
/// - [onPickFromGallery]: Callback when user selects "Choose from Gallery"
/// - [title]: Optional title text (defaults to "Chọn ảnh giấy tờ")
/// - [message]: Optional message text (defaults to "Vui lòng chọn nguồn ảnh để tiếp tục")
Future<void> showCupertinoPhotoSourcePicker({
  required BuildContext context,
  required VoidCallback onTakePhoto,
  required VoidCallback onPickFromGallery,
  String title = 'Chọn ảnh giấy tờ',
  String message = 'Vui lòng chọn nguồn ảnh để tiếp tục',
}) async {
  final isDark = Theme.of(context).brightness == Brightness.dark;
  final cancelTextColor = isDark ? Colors.white70 : AppColors.textSecondary;

  await showCupertinoModalPopup(
    context: context,
    builder: (BuildContext context) => CupertinoActionSheet(
      actions: [
        CupertinoActionSheetAction(
          onPressed: () {
            Navigator.pop(context);
            onTakePhoto();
          },
          child: const Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                CupertinoIcons.camera,
                color: AppColors.primaryColor,
                size: 20,
              ),
              SizedBox(width: 8),
              Text(
                'Chụp ảnh',
                style: TextStyle(
                  color: AppColors.primaryColor,
                  fontSize: 17,
                ),
              ),
            ],
          ),
        ),
        CupertinoActionSheetAction(
          onPressed: () {
            Navigator.pop(context);
            onPickFromGallery();
          },
          child: const Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                CupertinoIcons.photo_on_rectangle,
                color: AppColors.primaryColor,
                size: 20,
              ),
              SizedBox(width: 8),
              Text(
                'Chọn từ thư viện',
                style: TextStyle(
                  color: AppColors.primaryColor,
                  fontSize: 17,
                ),
              ),
            ],
          ),
        ),
      ],
      cancelButton: CupertinoActionSheetAction(
        isDestructiveAction: true,
        onPressed: () => Navigator.pop(context),
        child: Text(
          'Huỷ bỏ',
          style: TextStyle(
            color: cancelTextColor,
            fontSize: 17,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    ),
  );
} 