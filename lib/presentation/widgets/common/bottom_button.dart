import 'package:flutter/material.dart';
import 'common_button.dart';

class BottomButton extends StatelessWidget {
  final String? title;
  final Widget? child;
  final VoidCallback? onPressed;
  final bool enabled;
  final Color? backgroundColor;
  final Color? disabledColor;
  final Color? textColor;
  final EdgeInsets padding;

  const BottomButton({
    super.key,
    this.title,
    this.child,
    this.onPressed,
    this.enabled = true,
    this.backgroundColor,
    this.disabledColor,
    this.textColor,
    this.padding = const EdgeInsets.fromLTRB(16, 12, 16, 12),
  }) : assert(
          title != null || child != null,
          '<PERSON><PERSON>i cung cấp ít nhất một trong hai: title hoặc child',
        );

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        const Divider(height: 1),
        Safe<PERSON>rea(
          child: Padding(
            padding: padding,
            child: CommonButton(
              title: title,
              onPressed: onPressed,
              enabled: enabled,
              backgroundColor: backgroundColor,
              disabledColor: disabledColor,
              textColor: textColor,
              child: child,
            ),
          ),
        ),
      ],
    );
  }
}
