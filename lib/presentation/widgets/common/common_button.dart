import 'package:flutter/material.dart';
import 'package:sales_app/core/constants/app_dimens.dart';

class CommonButton extends StatelessWidget {
  final String? title;
  final Widget? child;
  final VoidCallback? onPressed;
  final double? width;
  final double? height;
  final Color? backgroundColor;
  final Color? disabledColor;
  final Color? textColor;
  final bool enabled;

  const CommonButton({
    super.key,
    this.title,
    this.child,
    this.onPressed,
    this.width,
    this.height,
    this.backgroundColor,
    this.disabledColor,
    this.textColor,
    this.enabled = true,
  }) : assert(
          title != null || child != null,
          '<PERSON><PERSON>i cung cấp ít nhất một trong hai: title hoặc child',
        );

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    // Xử lý nội dung button
    final buttonChild = child ??
        Text(
          title!,
          style: theme.textTheme.titleMedium?.copyWith(
            color: textColor ?? Colors.white,
            fontWeight: FontWeight.bold,
          ),
        );

    // X<PERSON> lý callback
    final callback = enabled ? onPressed : null;

    return SizedBox(
      width: width ?? double.infinity,
      height: height ?? AppDimens.buttonHeight,
      child: ElevatedButton(
        onPressed: callback,
        style: ElevatedButton.styleFrom(
          backgroundColor: backgroundColor ?? theme.colorScheme.primary,
          disabledBackgroundColor:
              disabledColor ?? theme.disabledColor.withValues(alpha: 0.6),
          shape: RoundedRectangleBorder(
            borderRadius: AppDimens.borderRadius12,
          ),
          minimumSize: const Size(0, AppDimens.buttonHeight),
          elevation: 0,
          padding: AppDimens.paddingHorizontalMd,
        ),
        child: buttonChild,
      ),
    );
  }
} 