import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:sales_app/core/theme/app_color.dart';
import 'package:sales_app/core/constants/app_dimens.dart';

class CommonDropdown<T> extends HookWidget {
  final List<T> items;
  final T? value;
  final String? label;
  final String? hint;
  final bool enabled;
  final Widget? prefixIcon;
  final Widget? trailingIcon;
  final bool isRequired;
  final String Function(T item)? labelBuilder;
  final Widget Function(T item)? itemBuilder;
  final Widget Function(T selected)? selectedItemBuilder;
  final void Function(T)? onChanged;
  final Color? backgroundColor;
  final Color? textColor;
  final Color? labelColor;
  final Color? iconColor;
  final Color? borderColor;
  final bool isDense;

  const CommonDropdown({
    super.key,
    required this.items,
    this.value,
    this.label,
    this.hint,
    this.enabled = true,
    this.prefixIcon,
    this.trailingIcon,
    this.isRequired = false,
    this.labelBuilder,
    this.itemBuilder,
    this.selectedItemBuilder,
    this.onChanged,
    this.backgroundColor,
    this.textColor,
    this.labelColor,
    this.iconColor,
    this.borderColor,
    this.isDense = false,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final hasFocus = useState(false);
    final focusNode = useFocusNode();

    final defaultTextColor = textColor ?? AppColors.textPrimary;
    final defaultLabelColor = labelColor ?? AppColors.textSecondary;
    final defaultIconColor = iconColor ?? AppColors.primaryColor.withValues(alpha: 0.7);
    final defaultBorderColor = borderColor ?? AppColors.primaryColor;

    // Listen to focus changes
    useEffect(() {
      void onFocusChange() {
        hasFocus.value = focusNode.hasFocus;
      }
      
      focusNode.addListener(onFocusChange);
      return () => focusNode.removeListener(onFocusChange);
    }, [focusNode]);

    String getItemLabel(T item) {
      if (labelBuilder != null) {
        return labelBuilder!(item);
      }
      return item.toString();
    }

    Widget buildSelectedItem(T item) {
      if (selectedItemBuilder != null) {
        return selectedItemBuilder!(item);
      }
      return Text(
        getItemLabel(item),
        style: theme.textTheme.bodyLarge?.copyWith(
          color: defaultTextColor,
          fontSize: 16,
        ),
      );
    }

    Widget buildItem(T item) {
      if (itemBuilder != null) {
        return itemBuilder!(item);
      }
      return Padding(
        padding: AppDimens.paddingHorizontalMd,
        child: Text(
          getItemLabel(item),
          style: theme.textTheme.bodyLarge?.copyWith(
            color: defaultTextColor,
            fontSize: 16,
          ),
        ),
      );
    }

    Future<void> showDropdown() async {
      if (!enabled) return;

      focusNode.requestFocus();

      if (Platform.isIOS) {
        await showCupertinoModalPopup(
          context: context,
          builder: (context) => Container(
            height: MediaQuery.of(context).size.height * 0.4,
            padding: const EdgeInsets.only(top: 6.0),
            margin: EdgeInsets.only(
              bottom: MediaQuery.of(context).viewInsets.bottom,
            ),
            color: CupertinoColors.systemBackground.resolveFrom(context),
            child: SafeArea(
              top: false,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    height: 44,
                    decoration: BoxDecoration(
                      border: Border(
                        bottom: BorderSide(
                          color: CupertinoColors.separator.resolveFrom(context),
                        ),
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        CupertinoButton(
                          child: const Text('Huỷ'),
                          onPressed: () => Navigator.pop(context),
                        ),
                        Text(
                          label ?? '',
                          style: const TextStyle(
                            fontSize: 17,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        CupertinoButton(
                          child: const Text('Xong'),
                          onPressed: () => Navigator.pop(context),
                        ),
                      ],
                    ),
                  ),
                  Expanded(
                    child: CupertinoPicker(
                      scrollController: FixedExtentScrollController(
                        initialItem: value != null
                            ? (items.contains(value) ? items.indexOf(value as T) : 0)
                            : 0,
                      ),
                      itemExtent: 40,
                      onSelectedItemChanged: (index) {
                        onChanged?.call(items[index]);
                      },
                      children: items.map((item) => Center(
                        child: Text(
                          getItemLabel(item),
                          style: const TextStyle(fontSize: 16),
                        ),
                      )).toList(),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      } else {
        await showModalBottomSheet(
          context: context,
          backgroundColor: backgroundColor ?? theme.colorScheme.surface,
          shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.vertical(
              top: Radius.circular(16),
            ),
          ),
          builder: (context) {
            return Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  padding: AppDimens.paddingVerticalMd,
                  decoration: BoxDecoration(
                    border: Border(
                      bottom: BorderSide(
                        color: theme.dividerColor,
                      ),
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const SizedBox(width: 48),
                      Text(
                        label ?? '',
                        style: theme.textTheme.titleMedium,
                      ),
                      IconButton(
                        icon: const Icon(Icons.close),
                        onPressed: () => Navigator.pop(context),
                      ),
                    ],
                  ),
                ),
                Flexible(
                  child: ListView.builder(
                    shrinkWrap: true,
                    itemCount: items.length,
                    itemBuilder: (context, index) {
                      final item = items[index];
                      final isSelected = value == item;
                      return InkWell(
                        onTap: () {
                          Navigator.pop(context);
                          onChanged?.call(item);
                        },
                        child: Container(
                          padding: AppDimens.paddingVerticalMd,
                          color: isSelected
                              ? defaultBorderColor.withValues(alpha: 0.1)
                              : null,
                          child: Row(
                            children: [
                              Expanded(child: buildItem(item)),
                              if (isSelected)
                                Padding(
                                  padding: AppDimens.paddingHorizontalMd,
                                  child: Icon(
                                    Icons.check,
                                    color: defaultBorderColor,
                                    size: 20,
                                  ),
                                ),
                            ],
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ],
            );
          },
        );
      }

      focusNode.unfocus();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        GestureDetector(
          onTap: showDropdown,
          child: Container(
            decoration: BoxDecoration(
              color: backgroundColor ?? (hasFocus.value 
                  ? defaultBorderColor.withValues(alpha: 0.05)
                  : theme.colorScheme.surface),
              borderRadius: AppDimens.borderRadius12,
              border: Border.all(
                color: hasFocus.value 
                    ? defaultBorderColor
                    : defaultBorderColor.withValues(alpha: 0.2),
                width: hasFocus.value ? 2 : 1,
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (label != null) ...[
                  Padding(
                    padding: EdgeInsets.only(
                      left: isDense ? 12 : 16,
                      right: isDense ? 12 : 16,
                      top: isDense ? 8 : 12,
                    ),
                    child: Row(
                      children: [
                        Text(
                          label!,
                          style: TextStyle(
                            color: hasFocus.value 
                                ? defaultBorderColor
                                : defaultLabelColor,
                            fontSize: 11,
                          ),
                        ),
                        if (isRequired)
                          Text(
                            ' *',
                            style: TextStyle(
                              color: theme.colorScheme.error,
                              fontSize: 11,
                            ),
                          ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 4),
                ],
                Padding(
                  padding: EdgeInsets.only(
                    left: isDense ? 12 : 16,
                    right: isDense ? 12 : 16,
                    bottom: isDense ? 8 : 12,
                  ),
                  child: Row(
                    children: [
                      if (prefixIcon != null) ...[
                        IconTheme(
                          data: IconThemeData(
                            color: defaultIconColor,
                            size: 20,
                          ),
                          child: prefixIcon!,
                        ),
                        const SizedBox(width: 8),
                      ],
                      Expanded(
                        child: value != null
                            ? buildSelectedItem(value as T)
                            : Text(
                                hint ?? '',
                                style: theme.textTheme.bodyLarge?.copyWith(
                                  color: defaultLabelColor.withValues(alpha: 0.5),
                                  fontSize: 16,
                                ),
                              ),
                      ),
                      IconTheme(
                        data: IconThemeData(
                          color: defaultIconColor,
                          size: 20,
                        ),
                        child: trailingIcon ?? const Icon(Icons.arrow_drop_down),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
} 