/// Network Monitoring System
///
/// A comprehensive network monitoring system that provides:
/// - Real-time network connectivity monitoring
/// - Automatic overlay for disconnected state
/// - SnackBar notifications for reconnection and unstable connections
/// - Global network awareness throughout the app
/// - Stability tracking and unstable connection detection
///
/// ## Features:
/// - **Adaptive Design**: Works natively on both iOS and Android
/// - **Smart Detection**: Distinguishes between disconnected and unstable connections
/// - **Non-intrusive**: Overlay only for disconnected, notifications for other states
/// - **Customizable**: Configurable overlay and notification behavior
/// - **Performance**: Efficient monitoring with minimal resource usage
///
/// ## Usage:
/// ```dart
/// // 1. Wrap your MaterialApp
/// MaterialApp.router(
///   builder: NetworkAwareAppBuilder.build,
///   // ... other configs
/// )
///
/// // 2. Or use custom configuration
/// MaterialApp.router(
///   builder: (context, child) => NetworkAwareAppBuilder.buildCustom(
///     context,
///     child,
///     showOverlay: true,
///     showNotifications: true,
///   ),
///   // ... other configs
/// )
///
/// // 3. Access network state in widgets
/// class MyWidget extends ConsumerWidget {
///   @override
///   Widget build(BuildContext context, WidgetRef ref) {
///     final networkState = ref.watch(networkMonitorControllerProvider);
///     
///     if (networkState.isDisconnected) {
///       return OfflineContent();
///     }
///     
///     return OnlineContent();
///   }
/// }
///
/// // 4. Manual network check
/// ref.read(networkMonitorControllerProvider.notifier).checkConnection();
/// ```
///
/// ## Network States:
/// - **Connected**: Normal internet connection
/// - **Disconnected**: No internet connection (shows overlay)
/// - **Unstable**: Frequent disconnections (shows notifications)
/// - **Checking**: Currently checking connection status
///
/// ## Architecture:
/// - **NetworkState**: Freezed state definition
/// - **NetworkMonitorController**: Riverpod controller for state management
/// - **NetworkWarningOverlay**: Full-screen overlay for disconnected state
/// - **NetworkNotificationService**: SnackBar notifications for state changes
/// - **NetworkAwareApp**: Global wrapper for the entire app
///
library network;

// Core enum and controller
export 'package:sales_app/core/enums/network_connection_status.dart';
export 'package:sales_app/presentation/controllers/network_monitor_controller.dart';

// UI components
export 'network_warning_overlay.dart';
export 'network_notification_service.dart';
export 'network_aware_app.dart';

// Convenience exports for common use cases
export 'package:sales_app/core/enums/network_connection_status.dart'
    show NetworkConnectionStatus, NetworkConnectionStatusExtension;
export 'package:sales_app/presentation/controllers/network_monitor_controller.dart'
    show NetworkMonitorController, networkMonitorControllerProvider;
export 'network_aware_app.dart'
    show NetworkAwareAppBuilder, NetworkAwareAppExtension;
export 'network_warning_overlay.dart'
    show NetworkWarningOverlayExtension;
export 'network_notification_service.dart'
    show NetworkNotificationExtension;
