# Network Monitoring System

> **🌐 Comprehensive Guide**: <PERSON><PERSON> thống giám sát mạng tự động với overlay và thông báo thông minh

## 📋 Overview

Hệ thống Network Monitoring cung cấp giám sát kết nối mạng real-time với:
- **Overlay tự động** khi mất kết nối
- **SnackBar notifications** cho reconnection và unstable connection
- **Smart detection** phân biệt disconnected vs unstable
- **Global integration** hoạt động trên toàn app

## 🏗️ Architecture

```
NetworkMonitorController (Riverpod)
├── NetworkState (Freezed)
├── NetworkWarningOverlay (UI)
├── NetworkNotificationService (SnackBar)
└── NetworkAwareApp (Global Wrapper)
```

## 🎯 Features

### ✅ Network States
- **Connected**: Kết nối bình thường
- **Disconnected**: M<PERSON>t kết nối (hiển thị overlay)
- **Unstable**: Kết nối không <PERSON><PERSON> định (hiển thị notification)
- **Checking**: <PERSON><PERSON> kiểm tra kết nối

### ✅ Smart Detection
- **Stability Tracking**: <PERSON>õ<PERSON> số lần disconnect trong 2 phút
- **Unstable Threshold**: 3+ disconnections = unstable
- **Auto Recovery**: Tự động reset sau 5 phút stable

### ✅ UI Components
- **Full-screen Overlay**: Cho disconnected state
- **SnackBar Notifications**: Cho reconnection/unstable
- **Adaptive Design**: iOS/Android native indicators
- **Customizable**: Colors, messages, behavior

## 🚀 Usage

### 1. Basic Setup (Already Integrated)

```dart
// main.dart - Đã được tích hợp
MaterialApp.router(
  builder: NetworkAwareAppBuilder.build,
  // ... other configs
)
```

### 2. Access Network State

```dart
class MyWidget extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final networkState = ref.watch(networkMonitorControllerProvider);
    
    return networkState.when(
      connected: () => OnlineContent(),
      disconnected: () => OfflineMessage(),
      unstable: () => UnstableWarning(),
      checking: () => CheckingIndicator(),
    );
  }
}
```

### 3. Manual Network Check

```dart
// Trigger manual check
ref.read(networkMonitorControllerProvider.notifier).checkConnection();

// Force connected state (for testing)
ref.read(networkMonitorControllerProvider.notifier).forceConnected();
```

### 4. Custom Integration

```dart
// Custom wrapper with specific config
MaterialApp.router(
  builder: (context, child) => NetworkAwareAppBuilder.buildCustom(
    context,
    child,
    showOverlay: true,        // Show overlay for disconnected
    showNotifications: true,  // Show SnackBar notifications
    onNetworkRetry: () {
      // Custom retry logic
    },
  ),
)

// Widget-level integration
Widget myWidget = MyContent().withNetworkAwareness(
  showOverlay: false,  // Disable overlay for this widget
);
```

## 📱 UI Behavior

### Disconnected State
- **Full-screen overlay** với retry button
- **Blocking interaction** until reconnected
- **"Continue" button** để force bypass (emergency)

### Unstable State  
- **SnackBar warning** không block UI
- **Background monitoring** tiếp tục
- **Auto-clear** sau 5 phút stable

### Reconnection
- **Success SnackBar** với green color
- **Auto-hide overlay** nếu đang hiển thị
- **Reset stability tracking**

## 🔧 Configuration

### Network Detection Settings

```dart
// In NetworkMonitorController
static const int _unstableThreshold = 3;           // 3 disconnections
static const Duration _unstableWindow = Duration(minutes: 2);  // in 2 minutes
static const Duration _checkDelay = Duration(seconds: 2);      // Check delay
```

### UI Customization

```dart
// Custom overlay colors
NetworkWarningOverlay(
  backgroundColor: Colors.red,
  opacity: 0.9,
  messageStyle: TextStyle(fontSize: 18),
  onRetry: () => customRetryLogic(),
)

// Custom notification service
NetworkNotificationService.initialize(scaffoldMessenger);
```

## 🧪 Testing

### Manual Testing

```dart
// Simulate network states
final controller = ref.read(networkMonitorControllerProvider.notifier);

// Test disconnection
controller.handleConnectivityChange(false);

// Test reconnection  
controller.handleConnectivityChange(true);

// Test unstable (multiple disconnections)
for (int i = 0; i < 4; i++) {
  controller.handleConnectivityChange(false);
  await Future.delayed(Duration(seconds: 10));
  controller.handleConnectivityChange(true);
  await Future.delayed(Duration(seconds: 5));
}
```

### Integration Testing

```dart
testWidgets('Network overlay shows on disconnection', (tester) async {
  // Setup
  await tester.pumpWidget(MyApp());
  
  // Trigger disconnection
  final container = ProviderScope.containerOf(tester.element(find.byType(MyApp)));
  container.read(networkMonitorControllerProvider.notifier)
    .handleConnectivityChange(false);
  
  await tester.pump();
  
  // Verify overlay is shown
  expect(find.byType(NetworkWarningOverlay), findsOneWidget);
  expect(find.text('Mất kết nối mạng'), findsOneWidget);
});
```

## 📊 Performance

### Resource Usage
- **Minimal CPU**: Chỉ listen connectivity stream
- **Low Memory**: Lightweight state management
- **Efficient UI**: Conditional rendering, no unnecessary rebuilds

### Battery Impact
- **Optimized**: Sử dụng system connectivity APIs
- **Smart Polling**: Không poll liên tục
- **Background Aware**: Pause monitoring khi app background

## 🔍 Debugging

### Logging
```dart
// Enable detailed logging
AppLogger.info('Network state changed', data: {
  'previous': previousState.toString(),
  'current': currentState.toString(),
  'timestamp': DateTime.now().toIso8601String(),
});
```

### Debug Tools
```dart
// Check current state
final state = ref.read(networkMonitorControllerProvider);
print('Current network state: $state');

// Manual state changes (for testing)
ref.read(networkMonitorControllerProvider.notifier).forceConnected();
```

## 🚨 Error Handling

### Graceful Degradation
- **Service unavailable**: Fallback to manual check
- **Permission denied**: Show appropriate message
- **Stream errors**: Auto-retry with exponential backoff

### Error Recovery
```dart
// Auto-recovery on stream errors
_connectivity.onConnectivityChanged.listen(
  (result) => _handleConnectivityChange(result),
  onError: (error) {
    AppLogger.error('Connectivity stream error', error: error);
    // Fallback to manual checking
    _startManualChecking();
  },
);
```

## 📈 Future Enhancements

### Planned Features
- [ ] **Connection Quality**: Measure speed/latency
- [ ] **Offline Mode**: Cache management integration  
- [ ] **Custom Retry Logic**: Per-screen retry strategies
- [ ] **Analytics**: Network usage tracking
- [ ] **Bandwidth Detection**: Adapt UI based on connection speed

### Integration Points
- [ ] **API Interceptors**: Auto-retry on network errors
- [ ] **Cache System**: Offline data management
- [ ] **Push Notifications**: Network-aware delivery
- [ ] **Background Sync**: Queue operations for later
