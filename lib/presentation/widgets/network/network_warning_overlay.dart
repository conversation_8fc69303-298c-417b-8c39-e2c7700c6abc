import 'dart:io';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:sales_app/core/theme/app_color.dart';
import 'package:sales_app/core/constants/app_dimens.dart';
import 'package:sales_app/presentation/controllers/network_monitor_controller.dart';
import 'package:sales_app/presentation/controllers/network_state.dart';
import 'package:sales_app/presentation/utils/network_state_localizer.dart';
import 'package:sales_app/presentation/widgets/network/network_notification_service.dart';

/// Network warning overlay that shows when connection is lost or unstable
/// Extends the existing AdaptiveLoadingOverlay pattern for consistency
class NetworkWarningOverlay extends ConsumerWidget {
  final Color? backgroundColor;
  final double opacity;
  final TextStyle? messageStyle;
  final VoidCallback? onRetry;

  const NetworkWarningOverlay({
    super.key,
    this.backgroundColor,
    this.opacity = 0.85,
    this.messageStyle,
    this.onRetry,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final networkState = ref.watch(networkMonitorControllerProvider);
    
    // Only show for disconnected or unstable states
    if (networkState.isConnected) {
      return const SizedBox.shrink();
    }

    return Material(
      color: Colors.transparent,
      child: Container(
        width: double.infinity,
        height: double.infinity,
        color: (backgroundColor ?? Colors.black).withValues(alpha: opacity),
        child: Center(
          child: Container(
            margin: EdgeInsets.all(AppDimens.spacingLG),
            padding: EdgeInsets.all(AppDimens.spacingLG),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              borderRadius: AppDimens.borderRadius12,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Network status icon
                _buildStatusIcon(context, networkState),
                
                SizedBox(height: AppDimens.spacingMD),

                // Status message
                _buildStatusMessage(context, networkState),

                SizedBox(height: AppDimens.spacingMD),

                // Description
                _buildDescription(context, networkState),

                SizedBox(height: AppDimens.spacingLG),
                
                // Action buttons
                _buildActionButtons(context, ref, networkState),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// Build status icon based on network state
  Widget _buildStatusIcon(BuildContext context, NetworkState networkState) {
    return Container(
      width: 80,
      height: 80,
      decoration: BoxDecoration(
        color: _getStatusColor(networkState).withValues(alpha: 0.1),
        shape: BoxShape.circle,
      ),
      child: Icon(
        _getStatusIconData(networkState),
        size: 40,
        color: _getStatusColor(networkState),
      ),
    );
  }

  /// Build status message
  Widget _buildStatusMessage(BuildContext context, NetworkState networkState) {
    return Text(
      NetworkStateLocalizer.getStatusMessage(context, networkState),
      style: messageStyle ??
        Theme.of(context).textTheme.headlineSmall?.copyWith(
          fontWeight: FontWeight.bold,
          color: Theme.of(context).colorScheme.onSurface,
        ),
      textAlign: TextAlign.center,
    );
  }

  /// Build description based on network state
  Widget _buildDescription(BuildContext context, NetworkState networkState) {
    final description = NetworkStateLocalizer.getDescription(context, networkState);

    if (description.isEmpty) return const SizedBox.shrink();

    return Text(
      description,
      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
        color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
      ),
      textAlign: TextAlign.center,
    );
  }

  /// Build action buttons
  Widget _buildActionButtons(BuildContext context, WidgetRef ref, NetworkState networkState) {
    final controller = ref.read(networkMonitorControllerProvider.notifier);
    
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        // Retry button
        Expanded(
          child: ElevatedButton.icon(
            onPressed: networkState.isChecking
                ? null
                : () {
                    onRetry?.call();
                    NetworkNotificationService.showRetryNotification(context);
                    controller.checkConnection();
                  },
            icon: networkState.isChecking
                ? _buildAdaptiveLoadingIndicator(context)
                : const Icon(Icons.refresh),
            label: Text(NetworkStateLocalizer.getRetryButtonText(context, networkState)),
            style: ElevatedButton.styleFrom(
              backgroundColor: _getStatusColor(networkState),
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: AppDimens.borderRadius8,
              ),
            ),
          ),
        ),
        
        SizedBox(width: AppDimens.spacingMD),
        
        // Continue offline button (for unstable/disconnected)
        if (networkState.isDisconnected || networkState.isUnstable)
          Expanded(
            child: OutlinedButton(
              onPressed: () {
                // Force connected state to allow user to continue
                controller.forceConnected();
              },
              style: OutlinedButton.styleFrom(
                side: BorderSide(color: Theme.of(context).colorScheme.outline),
                shape: RoundedRectangleBorder(
                  borderRadius: AppDimens.borderRadius8,
                ),
              ),
              child: Text(NetworkStateLocalizer.getContinueButtonText(context)),
            ),
          ),
      ],
    );
  }

  /// Build adaptive loading indicator
  Widget _buildAdaptiveLoadingIndicator(BuildContext context) {
    if (Platform.isIOS) {
      return const CupertinoActivityIndicator(
        radius: 10,
        color: Colors.white,
      );
    } else {
      return const SizedBox(
        width: 20,
        height: 20,
        child: CircularProgressIndicator(
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
        ),
      );
    }
  }

  /// Get status color based on network state
  Color _getStatusColor(NetworkState networkState) {
    return networkState.when(
      connected: () => AppColors.success,
      disconnected: () => AppColors.error,
      unstable: () => AppColors.warning,
      checking: () => AppColors.primaryColor,
    );
  }

  /// Get status icon data based on network state
  IconData _getStatusIconData(NetworkState networkState) {
    return networkState.when(
      connected: () => Icons.wifi,
      disconnected: () => Icons.wifi_off,
      unstable: () => Icons.signal_wifi_bad,
      checking: () => Icons.wifi_find,
    );
  }
}

/// Extension to easily show network warning overlay
extension NetworkWarningOverlayExtension on Widget {
  /// Wrap widget with network warning overlay
  Widget withNetworkWarning({
    Color? backgroundColor,
    double opacity = 0.85,
    TextStyle? messageStyle,
    VoidCallback? onRetry,
  }) {
    return Stack(
      children: [
        this,
        NetworkWarningOverlay(
          backgroundColor: backgroundColor,
          opacity: opacity,
          messageStyle: messageStyle,
          onRetry: onRetry,
        ),
      ],
    );
  }
}
