import 'package:flutter/material.dart';
import '../../router/navigation_extensions.dart';

/// Demo screen to test navigation functionality
/// Located in presentation layer for Clean Architecture compliance
class NavigationDemo extends StatelessWidget {
  const NavigationDemo({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Navigation Demo'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              const Text(
                'Go Router Navigation Demo',
                style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 32),
              
              // Push Navigation (with back button)
              _buildSection(
                'Push Navigation (with back button)',
                Colors.green,
                [
                  _buildButton('Push to Select Account Type', () => context.goToSelectAccountType()),
                  _buildButton('Push to CTV Policy', () => context.goToCtvPolicy()),
                  _buildButton('Push to KYC ID Guide', () => context.goToKycIdGuide()),
                  _buildButton('Push to Identity Upload', () => context.goToIdentityUpload()),
                  _buildButton('Push to Personal Info', () => context.goToPersonalInfoConfirmation()),
                  _buildButton('Push to Registration Success', () => context.goToRegistrationSuccess()),
                ],
              ),
              
              const SizedBox(height: 24),
              
              // Replace Navigation (no back button)
              _buildSection(
                'Replace Navigation (no back button)',
                Colors.orange,
                [
                  _buildButton('Replace with Login', () => context.replaceWithLogin()),
                  _buildButton('Replace with Home', () => context.replaceWithHome()),
                ],
              ),
              
              const SizedBox(height: 24),
              
              // Utility Navigation
              _buildSection(
                'Utility Navigation',
                Colors.blue,
                [
                  _buildButton('Go Back', () => context.goBack()),
                  _buildButton('Push to Home', () => context.pushToHome()),
                ],
              ),
              
              const SizedBox(height: 32),
              
              // Navigation Info
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey[300]!),
                ),
                child: const Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Navigation Patterns:',
                      style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                    ),
                    SizedBox(height: 8),
                    Text('🟢 Push: Adds to stack, shows back button'),
                    Text('🟠 Replace: Clears stack, no back button'),
                    Text('🔵 Utility: Smart navigation helpers'),
                    SizedBox(height: 8),
                    Text(
                      'Test the back button behavior after each navigation!',
                      style: TextStyle(fontStyle: FontStyle.italic),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
  
  Widget _buildSection(String title, Color color, List<Widget> buttons) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: color,
          ),
        ),
        const SizedBox(height: 12),
        ...buttons,
      ],
    );
  }
  
  Widget _buildButton(String text, VoidCallback onPressed) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: ElevatedButton(
        onPressed: onPressed,
        child: Text(text),
      ),
    );
  }
}
