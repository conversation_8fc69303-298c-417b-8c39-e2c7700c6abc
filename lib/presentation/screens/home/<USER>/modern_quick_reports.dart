import 'package:flutter/material.dart';
import '../../../../core/theme/app_color.dart';
import '../../../../core/constants/app_dimens.dart';

class ModernQuickReports extends StatelessWidget {
  const ModernQuickReports({super.key});

  // Mock data theo SRS
  static const Map<String, dynamic> mockReportsData = {
    'totalCustomers': 120,
    'latePaymentAmount': 500000000.0, // 500 triệu VNĐ
    'lateDays': 15,
    'avgMonthlyDebt': 2000000000.0, // 2 tỷ VNĐ
  };

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: AppDimens.marginHorizontalLg.copyWith(top: AppDimens.spacingLG),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title
          Text(
            'Báo cáo nhanh',
            style: TextStyle(
              fontSize: AppDimens.fontLG,
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
          ),
          
          AppDimens.h16,

          // Grid báo cáo
          Container(
            decoration: BoxDecoration(
              color: AppColors.backgroundColor,
              borderRadius: AppDimens.borderRadius16,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.06),
                  blurRadius: 12,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Padding(
              padding: AppDimens.paddingAllLg,
              child: Column(
                children: [
                  // Row 1: Tổng khách hàng và Số ngày chậm nộp
                  Row(
                    children: [
                      Expanded(
                        child: _buildReportCard(
                          icon: Icons.people_outline,
                          iconColor: AppColors.primaryColor,
                          backgroundColor: AppColors.primaryColor.withValues(alpha: 0.1),
                          title: 'Tổng khách hàng',
                          value: mockReportsData['totalCustomers'].toString(),
                          unit: 'khách hàng',
                          trend: '+5.2%',
                          isPositiveTrend: true,
                        ),
                      ),
                      
                      AppDimens.w12,
                      
                      Expanded(
                        child: _buildReportCard(
                          icon: Icons.schedule_outlined,
                          iconColor: AppColors.warning,
                          backgroundColor: AppColors.warning.withValues(alpha: 0.1),
                          title: 'Ngày chậm nộp',
                          value: mockReportsData['lateDays'].toString(),
                          unit: 'ngày',
                          trend: '-2 ngày',
                          isPositiveTrend: true,
                        ),
                      ),
                    ],
                  ),
                  
                  AppDimens.h12,
                  
                  // Row 2: Số tiền chậm nộp và Bình quân dư nợ tháng
                  Row(
                    children: [
                      Expanded(
                        child: _buildReportCard(
                          icon: Icons.trending_down_outlined,
                          iconColor: AppColors.error,
                          backgroundColor: AppColors.error.withValues(alpha: 0.1),
                          title: 'Tiền chậm nộp',
                          value: _formatCurrency(mockReportsData['latePaymentAmount']),
                          unit: '',
                          trend: '+12.5%',
                          isPositiveTrend: false,
                        ),
                      ),
                      
                      AppDimens.w12,
                      
                      Expanded(
                        child: _buildReportCard(
                          icon: Icons.trending_up_outlined,
                          iconColor: AppColors.success,
                          backgroundColor: AppColors.success.withValues(alpha: 0.1),
                          title: 'BQ dư nợ/tháng',
                          value: _formatCurrency(mockReportsData['avgMonthlyDebt']),
                          unit: '',
                          trend: '+8.3%',
                          isPositiveTrend: true,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildReportCard({
    required IconData icon,
    required Color iconColor,
    required Color backgroundColor,
    required String title,
    required String value,
    required String unit,
    required String trend,
    required bool isPositiveTrend,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.surfaceColor,
        borderRadius: AppDimens.borderRadius12,
        border: Border.all(
          color: iconColor.withValues(alpha: 0.15),
          width: 1,
        ),
      ),
      child: Padding(
        padding: AppDimens.paddingAllMd,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header với icon và trend
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Container(
                  width: 36,
                  height: 36,
                  decoration: BoxDecoration(
                    color: backgroundColor,
                    borderRadius: AppDimens.borderRadius8,
                  ),
                  child: Icon(
                    icon,
                    color: iconColor,
                    size: 20,
                  ),
                ),
                
                // Trend indicator
                Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: AppDimens.spacingXS,
                    vertical: AppDimens.spacingXS / 2,
                  ),
                  decoration: BoxDecoration(
                    color: isPositiveTrend 
                        ? AppColors.success.withValues(alpha: 0.1)
                        : AppColors.error.withValues(alpha: 0.1),
                    borderRadius: AppDimens.borderRadius4,
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        isPositiveTrend 
                            ? Icons.keyboard_arrow_up 
                            : Icons.keyboard_arrow_down,
                        color: isPositiveTrend ? AppColors.success : AppColors.error,
                        size: 14,
                      ),
                      Text(
                        trend,
                        style: TextStyle(
                          fontSize: AppDimens.fontXS,
                          fontWeight: FontWeight.w600,
                          color: isPositiveTrend ? AppColors.success : AppColors.error,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            
            AppDimens.h12,
            
            // Title
            Text(
              title,
              style: TextStyle(
                fontSize: AppDimens.fontXS,
                color: AppColors.textSecondary,
                fontWeight: FontWeight.w500,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
            
            AppDimens.h4,
            
            // Value
            Text(
              value,
              style: TextStyle(
                fontSize: AppDimens.fontLG,
                fontWeight: FontWeight.bold,
                color: iconColor,
                height: 1.1,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
            
            // Unit (nếu có)
            if (unit.isNotEmpty) ...[
              AppDimens.h2,
              Text(
                unit,
                style: TextStyle(
                  fontSize: AppDimens.fontXS,
                  color: AppColors.textSecondary,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  String _formatCurrency(num amount) {
    if (amount >= 1000000000) {
      return '${(amount / 1000000000).toStringAsFixed(1)} tỷ';
    } else if (amount >= 1000000) {
      return '${(amount / 1000000).toStringAsFixed(1)} triệu';
    } else if (amount >= 1000) {
      return '${(amount / 1000).toStringAsFixed(1)} nghìn';
    }
    return amount.toStringAsFixed(0);
  }
}
