import 'package:flutter/material.dart';
import '../../../../core/theme/app_color.dart';
import '../../../../core/constants/app_dimens.dart';

class ModernRecentViewed extends StatefulWidget {
  const ModernRecentViewed({super.key});

  @override
  State<ModernRecentViewed> createState() => _ModernRecentViewedState();
}

class _ModernRecentViewedState extends State<ModernRecentViewed>
    with TickerProviderStateMixin {
  late TabController _tabController;

  // Mock data theo SRS
  final Map<String, List<Map<String, dynamic>>> mockRecentData = {
    'all': [
      {
        'type': 'customer',
        'name': '<PERSON>uyễn Văn A',
        'id': 'KH001',
        'viewTime': '2 giờ trước',
        'status': 'Đang xử lý',
        'amount': 500000000.0,
      },
      {
        'type': 'loan',
        'name': '<PERSON><PERSON><PERSON>n vay #LN2024001',
        'id': 'LN2024001',
        'viewTime': '3 giờ trước',
        'status': 'Đã duyệt',
        'amount': 800000000.0,
      },
      {
        'type': 'customer',
        'name': 'Trần Thị B',
        'id': 'KH002',
        'viewTime': '5 giờ trước',
        'status': 'Chờ duyệt',
        'amount': 300000000.0,
      },
    ],
    'customers': [
      {
        'name': 'Nguyễn Văn A',
        'id': 'KH001',
        'viewTime': '2 giờ trước',
        'status': 'Đang xử lý',
        'phone': '0987654321',
        'loanCount': 2,
      },
      {
        'name': 'Trần Thị B',
        'id': 'KH002',
        'viewTime': '5 giờ trước',
        'status': 'Chờ duyệt',
        'phone': '0976543210',
        'loanCount': 1,
      },
    ],
    'loans': [
      {
        'name': 'Khoản vay #LN2024001',
        'id': 'LN2024001',
        'viewTime': '3 giờ trước',
        'status': 'Đã duyệt',
        'amount': 800000000.0,
        'customer': 'Nguyễn Văn C',
      },
      {
        'name': 'Khoản vay #LN2024002',
        'id': 'LN2024002',
        'viewTime': '1 ngày trước',
        'status': 'Đang giải ngân',
        'amount': 1200000000.0,
        'customer': 'Phạm Văn D',
      },
    ],
  };

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: AppDimens.marginHorizontalLg.copyWith(top: AppDimens.spacingLG),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title
          Text(
            'Đã xem gần đây',
            style: TextStyle(
              fontSize: AppDimens.fontLG,
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
          ),

          AppDimens.h16,

          // Container chính
          Container(
            decoration: BoxDecoration(
              color: AppColors.backgroundColor,
              borderRadius: AppDimens.borderRadius16,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.06),
                  blurRadius: 12,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              children: [
                // Tab Bar
                Container(
                  decoration: BoxDecoration(
                    color: AppColors.surfaceColor,
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(AppDimens.borderRadius16.topLeft.x),
                      topRight: Radius.circular(AppDimens.borderRadius16.topRight.x),
                    ),
                  ),
                  child: TabBar(
                    controller: _tabController,
                    labelColor: AppColors.primaryColor,
                    unselectedLabelColor: AppColors.textSecondary,
                    indicatorColor: AppColors.primaryColor,
                    indicatorWeight: 3,
                    indicatorPadding: AppDimens.paddingHorizontalLg,
                    labelStyle: TextStyle(
                      fontSize: AppDimens.fontSM,
                      fontWeight: FontWeight.w600,
                    ),
                    unselectedLabelStyle: TextStyle(
                      fontSize: AppDimens.fontSM,
                      fontWeight: FontWeight.w500,
                    ),
                    tabs: const [
                      Tab(text: 'Tất cả'),
                      Tab(text: 'Khách hàng'),
                      Tab(text: 'Khoản vay'),
                    ],
                  ),
                ),

                // Tab Content
                SizedBox(
                  height: 300,
                  child: TabBarView(
                    controller: _tabController,
                    children: [
                      _buildAllTab(),
                      _buildCustomersTab(),
                      _buildLoansTab(),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAllTab() {
    final allItems = mockRecentData['all'] ?? [];

    if (allItems.isEmpty) {
      return _buildEmptyState('Chưa có dữ liệu xem gần đây');
    }

    return ListView.separated(
      padding: AppDimens.paddingAllLg,
      itemCount: allItems.length,
      separatorBuilder: (context, index) => AppDimens.h12,
      itemBuilder: (context, index) {
        final item = allItems[index];
        return _buildAllItem(item);
      },
    );
  }

  Widget _buildCustomersTab() {
    final customers = mockRecentData['customers'] ?? [];

    if (customers.isEmpty) {
      return _buildEmptyState('Chưa có khách hàng nào');
    }

    return ListView.separated(
      padding: AppDimens.paddingAllLg,
      itemCount: customers.length,
      separatorBuilder: (context, index) => AppDimens.h12,
      itemBuilder: (context, index) {
        final customer = customers[index];
        return _buildCustomerItem(customer);
      },
    );
  }

  Widget _buildLoansTab() {
    final loans = mockRecentData['loans'] ?? [];

    if (loans.isEmpty) {
      return _buildEmptyState('Chưa có khoản vay nào');
    }

    return ListView.separated(
      padding: AppDimens.paddingAllLg,
      itemCount: loans.length,
      separatorBuilder: (context, index) => AppDimens.h12,
      itemBuilder: (context, index) {
        final loan = loans[index];
        return _buildLoanItem(loan);
      },
    );
  }

  Widget _buildAllItem(Map<String, dynamic> item) {
    final isCustomer = item['type'] == 'customer';

    return Container(
      decoration: BoxDecoration(
        color: AppColors.surfaceColor,
        borderRadius: AppDimens.borderRadius12,
        border: Border.all(
          color: AppColors.textSecondary.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Padding(
        padding: AppDimens.paddingAllMd,
        child: Row(
          children: [
            // Icon
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: isCustomer
                    ? AppColors.primaryColor.withValues(alpha: 0.1)
                    : AppColors.success.withValues(alpha: 0.1),
                borderRadius: AppDimens.borderRadius8,
              ),
              child: Icon(
                isCustomer ? Icons.person_outline : Icons.receipt_long_outlined,
                color: isCustomer ? AppColors.primaryColor : AppColors.success,
                size: 20,
              ),
            ),

            AppDimens.w12,

            // Content
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    item['name'],
                    style: TextStyle(
                      fontSize: AppDimens.fontSM,
                      fontWeight: FontWeight.w600,
                      color: AppColors.textPrimary,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  AppDimens.h4,
                  Row(
                    children: [
                      Text(
                        item['id'],
                        style: TextStyle(
                          fontSize: AppDimens.fontXS,
                          color: AppColors.textSecondary,
                        ),
                      ),
                      AppDimens.w8,
                      Container(
                        width: 4,
                        height: 4,
                        decoration: BoxDecoration(
                          color: AppColors.textSecondary,
                          shape: BoxShape.circle,
                        ),
                      ),
                      AppDimens.w8,
                      Text(
                        item['viewTime'],
                        style: TextStyle(
                          fontSize: AppDimens.fontXS,
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            // Status
            _buildStatusChip(item['status']),
          ],
        ),
      ),
    );
  }

  Widget _buildCustomerItem(Map<String, dynamic> customer) {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.surfaceColor,
        borderRadius: AppDimens.borderRadius12,
        border: Border.all(
          color: AppColors.textSecondary.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Padding(
        padding: AppDimens.paddingAllMd,
        child: Row(
          children: [
            // Avatar
            CircleAvatar(
              radius: 20,
              backgroundColor: AppColors.primaryColor.withValues(alpha: 0.1),
              child: Text(
                customer['name'].substring(0, 1).toUpperCase(),
                style: TextStyle(
                  fontSize: AppDimens.fontMD,
                  fontWeight: FontWeight.bold,
                  color: AppColors.primaryColor,
                ),
              ),
            ),

            AppDimens.w12,

            // Content
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    customer['name'],
                    style: TextStyle(
                      fontSize: AppDimens.fontSM,
                      fontWeight: FontWeight.w600,
                      color: AppColors.textPrimary,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  AppDimens.h4,
                  Text(
                    '${customer['phone']} • ${customer['loanCount']} khoản vay',
                    style: TextStyle(
                      fontSize: AppDimens.fontXS,
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
            ),

            // Time
            Text(
              customer['viewTime'],
              style: TextStyle(
                fontSize: AppDimens.fontXS,
                color: AppColors.textSecondary,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLoanItem(Map<String, dynamic> loan) {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.surfaceColor,
        borderRadius: AppDimens.borderRadius12,
        border: Border.all(
          color: AppColors.textSecondary.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Padding(
        padding: AppDimens.paddingAllMd,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Text(
                    loan['name'],
                    style: TextStyle(
                      fontSize: AppDimens.fontSM,
                      fontWeight: FontWeight.w600,
                      color: AppColors.textPrimary,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                AppDimens.w8,
                _buildStatusChip(loan['status']),
              ],
            ),

            AppDimens.h8,

            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Khách hàng: ${loan['customer']}',
                      style: TextStyle(
                        fontSize: AppDimens.fontXS,
                        color: AppColors.textSecondary,
                      ),
                    ),
                    AppDimens.h2,
                    Text(
                      _formatCurrency(loan['amount']),
                      style: TextStyle(
                        fontSize: AppDimens.fontSM,
                        fontWeight: FontWeight.bold,
                        color: AppColors.success,
                      ),
                    ),
                  ],
                ),
                Text(
                  loan['viewTime'],
                  style: TextStyle(
                    fontSize: AppDimens.fontXS,
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusChip(String status) {
    Color backgroundColor;
    Color textColor;

    switch (status) {
      case 'Đã duyệt':
      case 'Đang giải ngân':
        backgroundColor = AppColors.success.withValues(alpha: 0.1);
        textColor = AppColors.success;
        break;
      case 'Đang xử lý':
      case 'Chờ duyệt':
        backgroundColor = AppColors.warning.withValues(alpha: 0.1);
        textColor = AppColors.warning;
        break;
      default:
        backgroundColor = AppColors.textSecondary.withValues(alpha: 0.1);
        textColor = AppColors.textSecondary;
    }

    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: AppDimens.spacingXS,
        vertical: AppDimens.spacingXS / 2,
      ),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: AppDimens.borderRadius4,
      ),
      child: Text(
        status,
        style: TextStyle(
          fontSize: AppDimens.fontXS,
          fontWeight: FontWeight.w600,
          color: textColor,
        ),
      ),
    );
  }

  Widget _buildEmptyState(String message) {
    return Center(
      child: Padding(
        padding: AppDimens.paddingAllLg,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.inbox_outlined,
              size: 48,
              color: AppColors.textSecondary.withValues(alpha: 0.5),
            ),
            AppDimens.h16,
            Text(
              message,
              style: TextStyle(
                fontSize: AppDimens.fontMD,
                color: AppColors.textSecondary,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  String _formatCurrency(num amount) {
    if (amount >= 1000000000) {
      return '${(amount / 1000000000).toStringAsFixed(1)} tỷ';
    } else if (amount >= 1000000) {
      return '${(amount / 1000000).toStringAsFixed(1)} triệu';
    } else if (amount >= 1000) {
      return '${(amount / 1000000).toStringAsFixed(1)} nghìn';
    }
    return '${amount.toStringAsFixed(0)} VNĐ';
  }
}