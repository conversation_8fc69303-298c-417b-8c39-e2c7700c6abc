import 'package:flutter/material.dart';
import '../../../../core/theme/app_color.dart';
import '../../../../core/constants/app_dimens.dart';
import '../../../../core/utils/app_logger.dart';

class ModernSearchBar extends StatelessWidget {
  const ModernSearchBar({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: AppDimens.marginHorizontalLg.copyWith(top: AppDimens.spacingLG),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Tìm kiếm nhanh',
            style: TextStyle(
              fontSize: AppDimens.fontLG,
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
          ),
          AppDimens.h12,
          Container(
            decoration: BoxDecoration(
              color: AppColors.backgroundColor,
              borderRadius: AppDimens.borderRadius16,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.08),
                  blurRadius: 12,
                  offset: const Offset(0, 4),
                ),
              ],
              border: Border.all(
                color: AppColors.surfaceColor,
                width: 1,
              ),
            ),
            child: TextField(
              style: TextStyle(
                fontSize: AppDimens.fontMD,
                color: AppColors.textPrimary,
              ),
              decoration: InputDecoration(
                hintText: 'Tìm khách hàng, khoản vay, CTV...',
                hintStyle: TextStyle(
                  color: AppColors.textSecondary,
                  fontSize: AppDimens.fontMD,
                ),
                prefixIcon: Container(
                  padding: AppDimens.paddingAllSm,
                  margin: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        AppColors.info,
                        AppColors.info.withValues(alpha: 0.8),
                      ],
                    ),
                    borderRadius: AppDimens.borderRadius8,
                  ),
                  child: Icon(
                    Icons.search,
                    color: AppColors.textWhite,
                    size: AppDimens.iconSM,
                  ),
                ),
                suffixIcon: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    IconButton(
                      icon: Icon(
                        Icons.mic,
                        color: AppColors.textSecondary,
                        size: AppDimens.iconSM,
                      ),
                      onPressed: () => _onVoiceSearchTap(context),
                    ),
                    IconButton(
                      icon: Icon(
                        Icons.qr_code_scanner,
                        color: AppColors.textSecondary,
                        size: AppDimens.iconSM,
                      ),
                      onPressed: () => _onQrScanTap(context),
                    ),
                    AppDimens.w8,
                  ],
                ),
                border: InputBorder.none,
                contentPadding: AppDimens.paddingAllLg,
              ),
              onChanged: _onSearchTextChanged,
              onSubmitted: _onSearchSubmitted,
            ),
          ),
          // AppDimens.h12,
          // Quick search suggestions
          // SingleChildScrollView(
          //   scrollDirection: Axis.horizontal,
          //   child: Row(
          //     children: [
          //       _buildQuickSearchChip('Khách hàng trễ hạn', Icons.warning, AppColors.warning),
          //       AppDimens.w8,
          //       _buildQuickSearchChip('Khoản vay mới', Icons.add_circle, AppColors.success),
          //       AppDimens.w8,
          //       _buildQuickSearchChip('CTV khu vực', Icons.location_on, AppColors.info),
          //       AppDimens.w8,
          //       _buildQuickSearchChip('Báo cáo tháng', Icons.analytics, AppColors.primaryColor),
          //     ],
          //   ),
          // ),
        ],
      ),
    );
  }



  void _onSearchTextChanged(String value) {
    // TODO: Implement real-time search
    AppLogger.debug('Search: $value');
  }

  void _onSearchSubmitted(String value) {
    // TODO: Implement search submission
    AppLogger.info('Search submitted: $value');
  }

  void _onVoiceSearchTap(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Tìm kiếm bằng giọng nói'),
        backgroundColor: AppColors.info,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _onQrScanTap(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Quét mã QR'),
        backgroundColor: AppColors.primaryColor,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
}
