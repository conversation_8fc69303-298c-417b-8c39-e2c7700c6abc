import 'package:flutter/material.dart';
import '../../../../core/theme/app_color.dart';
import '../../../../core/constants/app_dimens.dart';

class ModernProductInfo extends StatelessWidget {
  const ModernProductInfo({super.key});

  // Mock data theo SRS
  static const Map<String, dynamic> mockProductData = {
    'productName': 'Vay trả góp ngày',
    'documents': [
      'CCCD/CMND (bản gốc và photo)',
      '<PERSON><PERSON><PERSON><PERSON> tờ tài sản đảm bảo',
      '<PERSON><PERSON><PERSON> đồng mua bán/Sổ đỏ',
      '<PERSON><PERSON><PERSON> đơn thu nhập',
      '<PERSON><PERSON> kê ngân hàng 3 tháng gần nhất',
    ],
    'features': [
      'Lãi suất cạnh tranh từ 0.85%/tháng',
      'Thời hạn vay linh hoạt 3-60 tháng',
      '<PERSON><PERSON><PERSON> mức tối đa 5 tỷ VNĐ',
      '<PERSON><PERSON><PERSON><PERSON> ngân nhanh trong 24h',
    ],
  };

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: AppDimens.marginHorizontalLg.copyWith(top: AppDimens.spacingLG),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title
          Text(
            'Thông tin sản phẩm',
            style: TextStyle(
              fontSize: AppDimens.fontLG,
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
          ),
          
          AppDimens.h16,

          // Product info container
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  AppColors.primaryColor.withValues(alpha: 0.05),
                  AppColors.secondaryColor.withValues(alpha: 0.02),
                ],
              ),
              borderRadius: AppDimens.borderRadius16,
              border: Border.all(
                color: AppColors.primaryColor.withValues(alpha: 0.1),
                width: 1,
              ),
            ),
            child: Padding(
              padding: AppDimens.paddingAllLg,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Product name với icon
                  Row(
                    children: [
                      Container(
                        width: 48,
                        height: 48,
                        decoration: BoxDecoration(
                          color: AppColors.primaryColor,
                          borderRadius: AppDimens.borderRadius12,
                          boxShadow: [
                            BoxShadow(
                              color: AppColors.primaryColor.withValues(alpha: 0.3),
                              blurRadius: 8,
                              offset: const Offset(0, 4),
                            ),
                          ],
                        ),
                        child: const Icon(
                          Icons.account_balance_wallet_outlined,
                          color: Colors.white,
                          size: 24,
                        ),
                      ),
                      
                      AppDimens.w12,
                      
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              mockProductData['productName'],
                              style: TextStyle(
                                fontSize: AppDimens.fontLG,
                                fontWeight: FontWeight.bold,
                                color: AppColors.primaryColor,
                              ),
                            ),
                            AppDimens.h4,
                            Text(
                              'Sản phẩm vay ưu đãi dành cho cộng tác viên',
                              style: TextStyle(
                                fontSize: AppDimens.fontSM,
                                color: AppColors.textSecondary,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  
                  AppDimens.h16,
                  
                  // Tính năng nổi bật
                  Text(
                    'Tính năng nổi bật',
                    style: TextStyle(
                      fontSize: AppDimens.fontMD,
                      fontWeight: FontWeight.w600,
                      color: AppColors.textPrimary,
                    ),
                  ),
                  
                  AppDimens.h16,
                  
                  ...List.generate(
                    (mockProductData['features'] as List).length,
                    (index) => _buildFeatureItem(mockProductData['features'][index]),
                  ),
                  
                  AppDimens.h16,
                  
                  // Hồ sơ cần nộp
                  Text(
                    'Hồ sơ cần nộp',
                    style: TextStyle(
                      fontSize: AppDimens.fontMD,
                      fontWeight: FontWeight.w600,
                      color: AppColors.textPrimary,
                    ),
                  ),
                  
                  AppDimens.h16,
                  
                  ...List.generate(
                    (mockProductData['documents'] as List).length,
                    (index) => _buildDocumentItem(mockProductData['documents'][index]),
                  ),
                  
                  AppDimens.h16,
                  
                  // Action buttons
                  Row(
                    children: [
                      Expanded(
                        child: _buildActionButton(
                          title: 'Tạo khoản vay',
                          icon: Icons.add_circle_outline,
                          color: AppColors.primaryColor,
                          isOutlined: false,
                        ),
                      ),
                      
                      AppDimens.w12,
                      
                      Expanded(
                        child: _buildActionButton(
                          title: 'Xem chi tiết',
                          icon: Icons.info_outline,
                          color: AppColors.primaryColor,
                          isOutlined: true,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFeatureItem(String feature) {
    return Padding(
      padding: EdgeInsets.only(bottom: AppDimens.spacingXS),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 20,
            height: 20,
            decoration: BoxDecoration(
              color: AppColors.success.withValues(alpha: 0.1),
              borderRadius: AppDimens.borderRadius4,
            ),
            child: Icon(
              Icons.check,
              color: AppColors.success,
              size: 14,
            ),
          ),
          
          AppDimens.w8,
          
          Expanded(
            child: Text(
              feature,
              style: TextStyle(
                fontSize: AppDimens.fontSM,
                color: AppColors.textPrimary,
                fontWeight: FontWeight.w500,
                height: 1.4,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDocumentItem(String document) {
    return Padding(
      padding: EdgeInsets.only(bottom: AppDimens.spacingXS),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 20,
            height: 20,
            decoration: BoxDecoration(
              color: AppColors.warning.withValues(alpha: 0.1),
              borderRadius: AppDimens.borderRadius4,
            ),
            child: Icon(
              Icons.description_outlined,
              color: AppColors.warning,
              size: 14,
            ),
          ),
          
          AppDimens.w8,
          
          Expanded(
            child: Text(
              document,
              style: TextStyle(
                fontSize: AppDimens.fontSM,
                color: AppColors.textPrimary,
                fontWeight: FontWeight.w500,
                height: 1.4,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton({
    required String title,
    required IconData icon,
    required Color color,
    required bool isOutlined,
  }) {
    return Container(
      height: 48,
      decoration: BoxDecoration(
        color: isOutlined ? Colors.transparent : color,
        borderRadius: AppDimens.borderRadius12,
        border: Border.all(
          color: color,
          width: isOutlined ? 1.5 : 0,
        ),
        boxShadow: isOutlined ? null : [
          BoxShadow(
            color: color.withValues(alpha: 0.3),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: InkWell(
        onTap: () {
          // Handle button tap
        },
        borderRadius: AppDimens.borderRadius12,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              color: isOutlined ? color : Colors.white,
              size: 20,
            ),
            AppDimens.w8,
            Text(
              title,
              style: TextStyle(
                fontSize: AppDimens.fontSM,
                fontWeight: FontWeight.w600,
                color: isOutlined ? color : Colors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
