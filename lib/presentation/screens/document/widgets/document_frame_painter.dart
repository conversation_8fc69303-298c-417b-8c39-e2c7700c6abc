import 'package:flutter/material.dart';

class DocumentFramePainter extends CustomPainter {
  final double width;
  final double height;
  final double borderWidth;
  final double cornerLength;

  DocumentFramePainter({
    required this.width,
    required this.height,
    this.borderWidth = 4.0,
    this.cornerLength = 40,
  });

  @override
  void paint(Canvas canvas, Size size) {
    // Tính toán vị trí của khung chụp
    final documentRect = Rect.fromCenter(
      center: Offset(size.width / 2, size.height / 2),
      width: width,
      height: height,
    );

    // Vẽ overlay đen hoàn toàn cho toàn bộ màn hình
    final overlayPath = Path()
      ..addRect(Rect.fromLTWH(0, 0, size.width, size.height));

    // Cắt phần khung chụp ra khỏi overlay
    final holePath = Path()
      ..addRect(documentRect);
    
    // Tạo path cuối cùng bằng cách loại bỏ phần khung chụp
    final finalPath = Path.combine(
      PathOperation.difference,
      overlayPath,
      holePath,
    );

    // Vẽ overlay đen
    final overlayPaint = Paint()
      ..color = Colors.black
      ..style = PaintingStyle.fill;
    
    canvas.drawPath(finalPath, overlayPaint);

    // Vẽ viền khung chụp
    final borderPaint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.stroke
      ..strokeWidth = borderWidth;
    
    canvas.drawRect(documentRect, borderPaint);

    // Vẽ các góc chỉ dẫn
    _drawCorners(canvas, documentRect, borderPaint);
  }

  void _drawCorners(Canvas canvas, Rect rect, Paint paint) {
    // Góc trên bên trái
    canvas.drawLine(
      rect.topLeft,
      rect.topLeft.translate(cornerLength, 0),
      paint,
    );
    canvas.drawLine(
      rect.topLeft,
      rect.topLeft.translate(0, cornerLength),
      paint,
    );

    // Góc trên bên phải
    canvas.drawLine(
      rect.topRight,
      rect.topRight.translate(-cornerLength, 0),
      paint,
    );
    canvas.drawLine(
      rect.topRight,
      rect.topRight.translate(0, cornerLength),
      paint,
    );

    // Góc dưới bên trái
    canvas.drawLine(
      rect.bottomLeft,
      rect.bottomLeft.translate(cornerLength, 0),
      paint,
    );
    canvas.drawLine(
      rect.bottomLeft,
      rect.bottomLeft.translate(0, -cornerLength),
      paint,
    );

    // Góc dưới bên phải
    canvas.drawLine(
      rect.bottomRight,
      rect.bottomRight.translate(-cornerLength, 0),
      paint,
    );
    canvas.drawLine(
      rect.bottomRight,
      rect.bottomRight.translate(0, -cornerLength),
      paint,
    );
  }

  @override
  bool shouldRepaint(DocumentFramePainter oldDelegate) {
    return width != oldDelegate.width ||
        height != oldDelegate.height ||
        borderWidth != oldDelegate.borderWidth ||
        cornerLength != oldDelegate.cornerLength;
  }
}
