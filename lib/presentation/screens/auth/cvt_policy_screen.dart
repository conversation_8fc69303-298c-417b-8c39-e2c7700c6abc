import 'package:flutter/material.dart';
import 'package:sales_app/core/theme/app_color.dart';
import 'package:sales_app/core/constants/app_dimens.dart';
import 'package:sales_app/generated/l10n.dart';
import '../../widgets/common/base_screen.dart';
import '../../widgets/common/bottom_button.dart';

// Navigation imports (Clean Architecture compliant)
import '../../router/navigation_extensions.dart';

class CTVPolicyScreen extends StatefulWidget {
  const CTVPolicyScreen({super.key});

  @override
  State<CTVPolicyScreen> createState() => _CTVPolicyScreenState();
}

class _CTVPolicyScreenState extends State<CTVPolicyScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1000),
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeIn,
      ),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.1),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeOut,
      ),
    );

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BaseScreen(
      title: S.of(context).introduction,
      body: Column(
        children: [
          Expanded(
            child: SafeArea(
              child: FadeTransition(
                opacity: _fadeAnimation,
                child: SlideTransition(
                  position: _slideAnimation,
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 16.0, vertical: 12),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _FeatureInfoCard(),
                        const SizedBox(height: 16),
                        _PolicyContent(),
                        const SizedBox(height: 16),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
          BottomButton(
            title: S.of(context).register,
            onPressed: () {
              // Use Go Router navigation
              context.goToKycIdGuide();
            },
          ),
        ],
      ),
    );
  }
}

class _FeatureInfoCard extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppColors.buttonColor.withValues(alpha: 0.08),
        borderRadius: AppDimens.borderRadius24,
        border: Border.all(color: AppColors.primaryColor.withValues(alpha: 0.1)),
        boxShadow: [
          BoxShadow(
            color: AppColors.primaryColor.withValues(alpha: 0.03),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            width: 72,
            height: 72,
            decoration: BoxDecoration(
              color: AppColors.buttonColor,
              borderRadius: AppDimens.borderRadius16,
            ),
            child: const Center(
              child: Icon(Icons.percent, color: AppColors.textWhite, size: 40),
            ),
          ),
          const SizedBox(width: 20),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  S.of(context).dailyInstallmentLoanDesc,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: AppColors.textPrimary,
                    fontWeight: FontWeight.w500,
                    height: 1.4,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  '100 triệu VND',
                  style: theme.textTheme.titleMedium?.copyWith(
                    color: AppColors.textPrimary,
                    fontWeight: FontWeight.bold,
                    fontSize: 20,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class _PolicyContent extends StatelessWidget {
  Widget _sectionTitle(String text, BuildContext context) => Padding(
        padding: const EdgeInsets.only(bottom: 12.0),
        child: Text(
          text,
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
                color: AppColors.textPrimary,
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
        ),
      );

  Widget _sectionContent(List<String> lines, BuildContext context) => Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: lines
            .map((e) => Padding(
                  padding: const EdgeInsets.only(bottom: 8.0),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text('• ',
                          style: TextStyle(
                              fontSize: 16,
                              color: AppColors.textPrimary,
                              fontWeight: FontWeight.bold)),
                      Expanded(
                        child: Text(
                          e,
                          style: Theme.of(context)
                              .textTheme
                              .bodyMedium
                              ?.copyWith(
                                color: AppColors.textPrimary.withValues(alpha: 0.8),
                                height: 1.5,
                              ),
                        ),
                      ),
                    ],
                  ),
                ))
            .toList(),
      );

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: AppColors.backgroundColor,
        borderRadius: AppDimens.borderRadius24,
        border: Border.all(color: AppColors.primaryColor.withValues(alpha: 0.1)),
        boxShadow: [
          BoxShadow(
            color: AppColors.primaryColor.withValues(alpha: 0.03),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _sectionTitle(S.of(context).dailyInstallmentLoan, context),
          _sectionContent([
            S.of(context).dailyInstallmentLoanDesc,
            S.of(context).suitableFor,
          ], context),
          const SizedBox(height: 24),
          _sectionTitle(S.of(context).benefitsAsCTV, context),
          _sectionContent([
            S.of(context).flexibleWorkingHours,
            S.of(context).monthlyServiceFee,
            S.of(context).unlimitedIncome,
            S.of(context).workUniform,
            S.of(context).healthInsurance,
            S.of(context).holidayBonus,
          ], context),
          const SizedBox(height: 24),
          _sectionTitle(S.of(context).stepsToBecomeCTV, context),
          _sectionContent([
            S.of(context).vietnameseCitizen,
            S.of(context).goodBackground,
            S.of(context).ageRequirement,
            S.of(context).healthRequirement,
            S.of(context).assetRequirement,
            S.of(context).areaKnowledge,
            S.of(context).noOtherBank,
          ], context),
        ],
      ),
    );
  }
}
