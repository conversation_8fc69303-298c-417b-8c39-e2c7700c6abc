import 'package:flutter/material.dart';
import 'package:sales_app/core/theme/app_color.dart';
import 'package:sales_app/core/constants/app_dimens.dart';
import 'package:sales_app/generated/l10n.dart';
import '../../widgets/common/base_screen.dart';
import '../../widgets/common/bottom_button.dart';

// Navigation imports (Clean Architecture compliant)
import '../../router/navigation_extensions.dart';

class KycIdGuideScreen extends StatelessWidget {
  const KycIdGuideScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return BaseScreen(
      title: S.of(context).documentVerificationGuide,
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              padding: AppDimens.paddingAllLg,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Center(
                    child: Image.asset(
                      width: 180,
                      height: 180,
                      'assets/images/CCCD.png',
                      fit: BoxFit.contain,
                    ),
                  ),
                  AppDimens.h24,
                  Align(
                    alignment: Alignment.centerLeft,
                    child: Text(
                      S.of(context).notesWhenTakingDocuments,
                      style: theme.textTheme.titleMedium?.copyWith(
                        color: AppColors.textPrimary,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  AppDimens.h16,
                  const _BulletList(
                    items: [
                      'Giấy tờ còn hạn sử dụng. Là hình gốc, không scan và photocopy.',
                      'Chụp trong môi trường đủ ánh sáng.',
                      'Đảm bảo ảnh rõ nét, không bị mờ loá.',
                    ],
                  ),
                  AppDimens.h32,
                  Align(
                    alignment: Alignment.centerLeft,
                    child: Text(
                      S.of(context).avoidUsingImages,
                      style: theme.textTheme.titleMedium?.copyWith(
                        color: AppColors.textPrimary,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  AppDimens.h16,
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      _WarningIcon(
                        imageStr: 'assets/images/CCCD_blur.png',
                        label: 'Quá mờ',
                        color: AppColors.error,
                        bgColor: AppColors.error.withValues(alpha: 0.1),
                      ),
                      _WarningIcon(
                        imageStr: 'assets/images/CCCD_crop.png',
                        label: 'Mất góc',
                        color: AppColors.error,
                        bgColor: AppColors.error.withValues(alpha: 0.1),
                      ),
                      _WarningIcon(
                        imageStr: 'assets/images/CCCD_flare.png',
                        label: 'Loá sáng',
                        color: AppColors.error,
                        bgColor: AppColors.error.withValues(alpha: 0.1),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          BottomButton(
            title: S.of(context).continueText,
            onPressed: () {
              // Use Go Router navigation
              context.goToIdentityUpload();
            },
          ),
        ],
      ),
    );
  }
}

class _BulletList extends StatelessWidget {
  final List<String> items;
  const _BulletList({required this.items});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: items
          .map((e) => Padding(
                padding: const EdgeInsets.only(bottom: 8),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('• ',
                        style: TextStyle(
                            fontSize: 18, color: AppColors.primaryColor)),
                    Expanded(
                      child: Text(
                        e,
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: AppColors.textPrimary,
                        ),
                      ),
                    ),
                  ],
                ),
              ))
          .toList(),
    );
  }
}

class _WarningIcon extends StatelessWidget {
  final String imageStr;
  final String label;
  final Color color;
  final Color bgColor;
  const _WarningIcon({
    required this.imageStr,
    required this.label,
    required this.color,
    required this.bgColor,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Column(
      children: [
        Container(
          width: 90,
          height: 58,
          decoration: BoxDecoration(
            color: bgColor,
            borderRadius: AppDimens.borderRadius12,
            border: Border.all(color: color.withValues(alpha: 0.2)),
          ),
          child: Image.asset(
            imageStr,
            fit: BoxFit.contain,
          ),
        ),
        AppDimens.h8,
        Text(
          label,
          style: theme.textTheme.bodySmall?.copyWith(
            color: AppColors.error,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }
}
