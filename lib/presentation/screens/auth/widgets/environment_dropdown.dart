import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:sales_app/domain/entities/environment_entity.dart' as env;
import 'package:sales_app/domain/services/environment_service.dart';
import 'package:sales_app/core/utils/app_logger.dart';
import 'package:sales_app/core/constants/environment_constants.dart';
import 'package:sales_app/di/injection.dart';
import '../../../widgets/common/common_dropdown.dart';

/// Environment dropdown widget for selecting Dev/Staging/Prod environments
/// Uses CommonDropdown for consistent UI and behavior
class EnvironmentDropdown extends ConsumerWidget {
  final ValueChanged<env.Environment>? onChanged;
  final bool isDense;

  const EnvironmentDropdown({
    super.key,
    this.onChanged,
    this.isDense = true,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final environmentService = getIt<EnvironmentService>();

    return StreamBuilder<env.EnvironmentEntity>(
      stream: environmentService.environmentChanges,
      initialData: environmentService.currentConfig,
      builder: (context, snapshot) {
        final currentEnvironment = snapshot.data?.environment ?? env.Environment.dev;

        return CommonDropdown<env.Environment>(
          items: env.Environment.values,
          value: currentEnvironment,
          label: 'Environment',
          hint: 'Select Environment',
          isDense: isDense,
          onChanged: (environment) => _handleEnvironmentChange(
            environment,
            environmentService,
            onChanged,
          ),
          labelBuilder: (environment) => environment.displayName,
          selectedItemBuilder: (environment) => _buildSelectedItem(environment),
          itemBuilder: (environment) => _buildDropdownItem(environment),
          backgroundColor: Colors.white.withValues(alpha: 0.1),
          textColor: Colors.white,
          labelColor: Colors.white,
          iconColor: Colors.white.withValues(alpha: 0.7),
          borderColor: Colors.white.withValues(alpha: 0.3),
        );
      },
    );
  }

  /// Handle environment change
  void _handleEnvironmentChange(
    env.Environment? environment,
    EnvironmentService environmentService,
    ValueChanged<env.Environment>? onChanged,
  ) {
    if (environment == null) return;

    try {
      // Switch environment in service
      environmentService.switchEnvironment(environment);

      // Call external callback if provided
      onChanged?.call(environment);

      AppLogger.info('Environment changed to: ${environment.displayName}');
    } catch (e) {
      AppLogger.error('Failed to change environment: $e');
    }
  }

  /// Build selected item display
  Widget _buildSelectedItem(env.Environment environment) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        _buildEnvironmentIndicator(environment),
        const SizedBox(width: 8),
        Flexible(
          child: Text(
            environment.displayName,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  /// Build dropdown item
  Widget _buildDropdownItem(env.Environment environment) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          _buildEnvironmentIndicator(environment),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  environment.displayName,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  _getEnvironmentDescription(environment),
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Build environment indicator (colored dot)
  Widget _buildEnvironmentIndicator(env.Environment environment) {
    Color color;
    switch (environment) {
      case env.Environment.dev:
        color = Colors.green;
        break;
      case env.Environment.staging:
        color = Colors.orange;
        break;
      case env.Environment.prod:
        color = Colors.red;
        break;
    }

    return Container(
      width: 8,
      height: 8,
      decoration: BoxDecoration(
        color: color,
        shape: BoxShape.circle,
      ),
    );
  }

  /// Get environment description
  String _getEnvironmentDescription(env.Environment environment) {
    switch (environment) {
      case env.Environment.dev:
        return 'Development - ${EnvironmentConstants.devBaseUrl}';
      case env.Environment.staging:
        return 'Testing - ${EnvironmentConstants.stagingBaseUrl}';
      case env.Environment.prod:
        return 'Production - ${EnvironmentConstants.prodBaseUrl}';
    }
  }
}
