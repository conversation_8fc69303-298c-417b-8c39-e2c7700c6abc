import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:sales_app/core/constants/app_dimens.dart';

// Navigation imports (Clean Architecture compliant)
import '../../../router/navigation_extensions.dart';

class AccountTypeSelector extends StatelessWidget {
  const AccountTypeSelector({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Padding(
      padding: AppDimens.paddingHorizontalMd,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          AppDimens.h48,
          AppDimens.h16,
          _buildInfoCard(theme),
          AppDimens.h32,
          _buildHeader(context),
          AppDimens.h32,
          Expanded(
            child: _buildAccountTypeOptions(context: context, theme: theme),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    final theme = Theme.of(context);
    return Text(
      '<PERSON>ui lòng chọn loại tài khoản phù hợp với bạn',
      textAlign: TextAlign.center,
      style: theme.textTheme.bodyMedium?.copyWith(
        color: Colors.white.withValues(alpha: 0.9),
        fontSize: 15,
        shadows: [
          Shadow(
            color: Colors.black.withValues(alpha: 0.10),
            offset: const Offset(0, 1),
            blurRadius: 1,
          ),
        ],
      ),
    );
  }

  Widget _buildInfoCard(ThemeData theme) {
    return ClipRRect(
      borderRadius: AppDimens.borderRadius16,
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 8, sigmaY: 8),
        child: Container(
          padding: AppDimens.paddingAllMd,
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.3),
            borderRadius: AppDimens.borderRadius16,
            border: Border.all(color: Colors.white.withValues(alpha: 0.2)),
          ),
          child: Row(
            children: [
              Container(
                padding: AppDimens.paddingAllSm,
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.4),
                  shape: BoxShape.circle,
                ),
                child: const Icon(Icons.security, color: Color(0xFF292663)),
              ),
              AppDimens.w16,
              Expanded(
                child: Text(
                  'An toàn – Bền vững – Lợi nhuận hợp lý',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: const Color(0xFF292663).withValues(alpha: 0.7),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAccountTypeOptions(
      {required BuildContext context, required ThemeData theme}) {
    return ListView(
      physics: const BouncingScrollPhysics(),
      padding: AppDimens.paddingVerticalLg,
      children: [
        _buildAccountTypeOption(
          icon: Icons.person_outline,
          title: 'Cộng tác viên bán hàng',
          subtitle: 'Tham gia mạng lưới bán hàng linh hoạt',
          onTap: () {
            // Use Go Router navigation
            context.goToCtvPolicy();
          },
          theme: theme,
        ),
        AppDimens.h24,
        _buildAccountTypeOption(
          icon: Icons.apartment_outlined,
          title: 'Cán bộ KienlongBank',
          subtitle: 'Đăng ký tài khoản dành cho nhân sự nội bộ',
          onTap: () {
            // Navigator.push(context, MaterialPageRoute(builder: (_) => const CBNVScreen()));
          },
          theme: theme,
        ),
      ],
    );
  }

  Widget _buildAccountTypeOption({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
    required ThemeData theme,
  }) {
    return Center(
      child: Container(
        constraints: const BoxConstraints(maxWidth: 400),
        child: GestureDetector(
          onTap: onTap,
          child: Container(
            padding: AppDimens.paddingAllLg,
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.92),
              borderRadius: AppDimens.borderRadius24,
              boxShadow: const [
                BoxShadow(
                  color: Colors.black12,
                  blurRadius: 12,
                  offset: Offset(0, 6),
                ),
              ],
            ),
            child: Row(
              children: [
                Container(
                  padding: AppDimens.paddingAllMd,
                  decoration: BoxDecoration(
                    color: const Color(0xFF292663).withValues(alpha: 0.1),
                    borderRadius: AppDimens.borderRadius12,
                  ),
                  child: Icon(
                    icon,
                    color: const Color(0xFF292663),
                  ),
                ),
                AppDimens.w16,
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: theme.textTheme.titleSmall?.copyWith(
                          color: const Color(0xFF292663),
                          fontWeight: FontWeight.bold,
                          fontSize: 17,
                        ),
                      ),
                      AppDimens.h4,
                      Text(
                        subtitle,
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: const Color(0xFF292663).withValues(alpha: 0.7),
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
                const Icon(Icons.arrow_forward_ios_rounded,
                    size: 18, color: Color(0xFF292663)),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
