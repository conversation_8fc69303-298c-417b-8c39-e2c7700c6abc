import 'package:flutter/material.dart';
import 'package:sales_app/core/theme/app_color.dart';
import '../../../widgets/common/common_dropdown.dart';

class DocumentType {
  final String id;
  final String name;

  const DocumentType({
    required this.id,
    required this.name,
  });
}

class DocumentDropdown extends StatelessWidget {
  final String? value;
  final ValueChanged<String?>? onChanged;

  const DocumentDropdown({
    super.key,
    this.value,
    this.onChanged,
  });

  static const _documents = [
    DocumentType(
      id: 'cccd',
      name: 'CCCD',
    ),
    DocumentType(
      id: 'the_can_cuoc',
      name: 'Thẻ Căn <PERSON>ư<PERSON>',
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return CommonDropdown<DocumentType>(
      items: _documents,
      value: _documents.firstWhere(
        (doc) => doc.id == value,
        orElse: () => _documents.first, // Mặc định chọn CCCD
      ),
      label: '<PERSON>ại giấy tờ',
      hint: '<PERSON><PERSON>n loại giấy tờ',
      isRequired: true,
      onChanged: (doc) => onChanged?.call(doc.id),
      labelBuilder: (doc) => doc.name,
      backgroundColor: AppColors.surfaceColor,
      borderColor: AppColors.primaryColor.withValues(alpha: 0.2),
    );
  }
}
