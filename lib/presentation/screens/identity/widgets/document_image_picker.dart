import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:sales_app/core/theme/app_color.dart';
import 'package:sales_app/core/constants/app_dimens.dart';
import 'package:sales_app/core/services/image_picker_service.dart';
import 'package:sales_app/core/enums/document_side.dart';
import '../../document/capture_document_screen.dart';
import '../../../widgets/common/photo_source_picker.dart';

class DocumentImagePicker extends StatelessWidget {
  final String label;
  final String? imagePath;
  final Function(String path) onImageSelected;

  const DocumentImagePicker({
    super.key,
    required this.label,
    required this.onImageSelected,
    this.imagePath,
  });

  Future<void> _handleTap(BuildContext context) async {
    final imagePicker = GetIt.I<ImagePickerService>();
    
    await showCupertinoPhotoSourcePicker(
      context: context,
      onTakePhoto: () async {
        if (context.mounted) {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => CaptureDocumentScreen(
                title: 'Chụp $label',
                side: label.toLowerCase().contains('trước') 
                    ? DocumentSide.front 
                    : DocumentSide.back,
                onImageCaptured: (image) => onImageSelected(image.path),
              ),
            ),
          );
        }
      },
      onPickFromGallery: () async {
        final path = await imagePicker.pickFromGallery(
          maxWidth: 1920,
          maxHeight: 1080,
          quality: 90,
        );
        if (path != null) {
          onImageSelected(path);
        }
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: theme.textTheme.bodyMedium?.copyWith(
            color: AppColors.primaryColor,
            fontWeight: FontWeight.w500,
          ),
        ),
        AppDimens.h8,
        InkWell(
          onTap: () => _handleTap(context),
          borderRadius: AppDimens.borderRadius12,
          child: Container(
            height: 180,
            width: double.infinity,
            decoration: BoxDecoration(
              color: AppColors.primaryColor.withValues(alpha: 0.05),
              borderRadius: AppDimens.borderRadius12,
              border: Border.all(
                color: AppColors.primaryColor.withValues(alpha: 0.2),
              ),
            ),
            child: imagePath != null
                ? ClipRRect(
                    borderRadius: AppDimens.borderRadius12,
                    child: Image.file(
                      File(imagePath!),
                      fit: BoxFit.cover,
                    ),
                  )
                : Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.camera_alt_outlined,
                        color: AppColors.primaryColor.withValues(alpha: 0.7),
                        size: AppDimens.iconXL,
                      ),
                      AppDimens.h16,
                      Text(
                        'Chạm để chọn ảnh',
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: AppColors.primaryColor.withValues(alpha: 0.7),
                        ),
                      ),
                    ],
                  ),
          ),
        ),
      ],
    );
  }
}
