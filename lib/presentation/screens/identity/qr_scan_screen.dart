import 'package:flutter/material.dart';
import 'package:mobile_scanner/mobile_scanner.dart';
import 'package:sales_app/core/constants/app_dimens.dart';
import 'package:sales_app/presentation/router/router.dart';
import 'package:get_it/get_it.dart';
import 'package:sales_app/core/services/image_picker_service.dart';
import 'package:sales_app/presentation/widgets/shared/loading_overlay.dart';
import 'package:sales_app/core/utils/app_logger.dart';

class QRScanScreen extends StatefulWidget {
  const QRScanScreen({super.key});

  @override
  State<QRScanScreen> createState() => _QRScanScreenState();
}

class _QRScanScreenState extends State<QRScanScreen>
    with WidgetsBindingObserver {
  MobileScannerController? _controller;
  bool _isProcessing = false;
  bool _isFlashOn = false;
  bool _isFirstScan = true;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _initializeCamera();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _controller?.dispose();
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (_controller == null) return;

    if (state == AppLifecycleState.resumed) {
      _initializeCamera();
    } else if (state == AppLifecycleState.inactive) {
      _controller?.stop();
    }
  }

  Future<void> _initializeCamera() async {
    try {
      _controller?.dispose();
      _controller = MobileScannerController(
        facing: CameraFacing.back,
        torchEnabled: false,
      );
      setState(() {});
    } catch (e) {
      AppLogger.error('Error initializing camera', error: e);
    }
  }

  void _onDetect(BarcodeCapture capture) {
    if (_isProcessing || _controller == null) return;

    final List<Barcode> barcodes = capture.barcodes;
    if (barcodes.isEmpty) return;

    final Barcode barcode = barcodes.first;
    if (barcode.rawValue == null) return;

    setState(() => _isProcessing = true);

    // Xử lý QR code
    _processQRCode(barcode.rawValue!);
  }

  Future<void> _processQRCode(String qrData) async {
    try {
      // TODO: Implement actual QR code processing
      // For now, just simulate a delay
      await Future.delayed(const Duration(seconds: 2));

      if (!mounted) return;

      // Simulate invalid QR code for testing
      if (_isFirstScan) {
        setState(() => _isFirstScan = false);
        await Future.delayed(const Duration(seconds: 5));
        if (!mounted) return;
        setState(() => _isProcessing = false);
        _showErrorDialog(
            'Mã QR không hợp lệ. Vui lòng thử lại hoặc chọn Bỏ qua để dùng OCR.');
        return;
      }

      // Navigate to next screen
      context.goToPersonalInfoConfirmation();
    } catch (e) {
      if (!mounted) return;
      setState(() => _isProcessing = false);
      _showErrorDialog(e.toString());
    }
  }

  Future<void> _handleGalleryPick() async {
    if (_isProcessing) return;

    try {
      final imagePicker = GetIt.I<ImagePickerService>();
      final path = await imagePicker.pickFromGallery(
        maxWidth: 1920,
        maxHeight: 1080,
        quality: 90,
      );

      if (path == null) return;

      setState(() => _isProcessing = true);

      // TODO: Process the picked image for QR code
      // For now, just simulate a delay
      await Future.delayed(const Duration(seconds: 2));

      if (!mounted) return;

      // Simulate invalid QR code for testing
      if (_isFirstScan) {
        setState(() => _isFirstScan = false);
        await Future.delayed(const Duration(seconds: 5));
        if (!mounted) return;
        setState(() => _isProcessing = false);
        _showErrorDialog(
            'Mã QR không hợp lệ. Vui lòng thử lại hoặc chọn Bỏ qua để dùng OCR.');
        return;
      }

      // Navigate to next screen
      context.goToPersonalInfoConfirmation();
    } catch (e) {
      if (!mounted) return;
      setState(() => _isProcessing = false);
      _showErrorDialog(e.toString());
    }
  }

  void _toggleFlash() {
    if (_controller == null) return;

    setState(() {
      _isFlashOn = !_isFlashOn;
      _controller?.toggleTorch();
    });
  }

  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Lỗi'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final screenHeight =
        size.height - MediaQuery.of(context).padding.top - kToolbarHeight;

    return Scaffold(
      backgroundColor: Colors.black,
      body: LoadingOverlay(
        isLoading: _isProcessing,
        message: 'Đang xử lý...',
        child: Stack(
          fit: StackFit.expand,
          children: [
            // Scanner view
            if (_controller != null)
              MobileScanner(
                controller: _controller!,
                onDetect: _onDetect,
              )
            else
              const Center(
                child: CircularProgressIndicator(
                  color: Colors.white,
                ),
              ),

            // Overlay with scanning area
            Container(
              color: Colors.black.withValues(alpha: 0.5),
              child: Center(
                child: Container(
                  width: size.width * 0.7,
                  height: size.width * 0.7,
                  decoration: BoxDecoration(
                    border: Border.all(
                      color: Colors.white,
                      width: 2,
                    ),
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
            ),

            // Top bar with back button and logo
            Positioned(
              top: MediaQuery.of(context).padding.top + 16,
              left: 0,
              right: 0,
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    // Back button
                    IconButton(
                      onPressed:
                          _isProcessing ? null : () => Navigator.pop(context),
                      icon: const Icon(
                        Icons.arrow_back_ios_new_rounded,
                        color: Colors.white,
                        size: 24,
                      ),
                    ),
                    // KienlongBank logo
                    Image.asset(
                      'assets/images/kienlongbank_logo.png',
                      height: 32,
                      color: Colors.white,
                    ),
                    // Empty container for balance
                    const SizedBox(width: 40),
                  ],
                ),
              ),
            ),

            // Guidance text
            Positioned(
              top: screenHeight * 0.25,
              left: 0,
              right: 0,
              child: Container(
                margin: AppDimens.marginHorizontalLg,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      'Vui lòng đưa QR trên CCCD vào khung hình',
                      textAlign: TextAlign.center,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.w500,
                          ),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'Hoặc chọn ảnh từ thư viện',
                      textAlign: TextAlign.center,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Colors.white.withValues(alpha: 0.9),
                          ),
                    ),
                  ],
                ),
              ),
            ),

            // Bottom buttons
            Positioned(
              left: 0,
              right: 0,
              bottom: 48,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Action buttons row
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      // Flash toggle button
                      _ActionButton(
                        onPressed: _isProcessing ? null : _toggleFlash,
                        icon: _isFlashOn ? Icons.flash_on : Icons.flash_off,
                        label: _isFlashOn ? 'Tắt đèn' : 'Bật đèn',
                      ),
                      // Gallery button
                      _ActionButton(
                        onPressed: _isProcessing ? null : _handleGalleryPick,
                        icon: Icons.photo_library_outlined,
                        label: 'Thư viện',
                      ),
                    ],
                  ),
                  const SizedBox(height: 24),
                  // Skip button
                  TextButton(
                    onPressed: _isProcessing
                        ? null
                        : () {
                            context.goToPersonalInfoConfirmation();
                          },
                    child: const Text(
                      'Bỏ qua',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _ActionButton extends StatelessWidget {
  final IconData icon;
  final String label;
  final VoidCallback? onPressed;

  const _ActionButton({
    required this.icon,
    required this.label,
    this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 64,
          height: 64,
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.2),
            shape: BoxShape.circle,
          ),
          child: IconButton(
            onPressed: onPressed,
            icon: Icon(
              icon,
              color: Colors.white,
              size: 28,
            ),
          ),
        ),
        const SizedBox(height: 8),
        Text(
          label,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 14,
          ),
        ),
      ],
    );
  }
}
