import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:sales_app/core/theme/app_color.dart';
import 'package:sales_app/core/constants/app_dimens.dart';
import 'package:sales_app/presentation/screens/auth/widgets/document_dropdown.dart';
import '../../widgets/common/base_screen.dart';
import '../../widgets/common/bottom_button.dart';
import 'widgets/document_image_picker.dart';

// Navigation imports (Clean Architecture compliant)
import '../../router/navigation_extensions.dart';

class IdentityUploadPage extends HookWidget {
  const IdentityUploadPage({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final selectedDocType = useState<String>('cccd');
    final frontImage = useState<String?>(null);
    final backImage = useState<String?>(null);

    void handleImagePick(bool isFront, String path) {
      if (isFront) {
        frontImage.value = path;
      } else {
        backImage.value = path;
      }
    }

    final isFormValid = frontImage.value != null && backImage.value != null;
    return BaseScreen(
      title: '<PERSON>ung cấp giấy tờ tùy thân',
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              padding: AppDimens.paddingAllMd,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    padding: AppDimens.paddingAllMd,
                    decoration: BoxDecoration(
                      color: AppColors.primaryColor.withValues(alpha: 0.05),
                      borderRadius: AppDimens.borderRadius12,
                      border: Border.all(
                        color: AppColors.primaryColor.withValues(alpha: 0.1),
                      ),
                    ),
                    child: Text(
                      'Vui lòng chọn loại giấy tờ tùy thân của người đồng vay để xác thực thông tin',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: AppColors.primaryColor,
                        height: 1.5,
                      ),
                    ),
                  ),
                  AppDimens.h24,
                  DocumentDropdown(
                    value: selectedDocType.value,
                    onChanged: (value) => selectedDocType.value = value ?? 'cccd',
                  ),
                  AppDimens.h24,
                  DocumentImagePicker(
                    label: 'Ảnh mặt trước',
                    imagePath: frontImage.value,
                    onImageSelected: (path) => handleImagePick(true, path),
                  ),
                  AppDimens.h16,
                  DocumentImagePicker(
                    label: 'Ảnh mặt sau',
                    imagePath: backImage.value,
                    onImageSelected: (path) => handleImagePick(false, path),
                  ),
                ],
              ),
            ),
          ),
          BottomButton(
            title: 'Tiếp tục',
            onPressed: isFormValid
                ? () {
                    if (selectedDocType.value == 'cccd') {
                      context.goToQRScan();
                    } else {
                      context.goToPersonalInfoConfirmation();
                    }
                  }
                : null,
            enabled: isFormValid,
          ),
        ],
      ),
    );
  }
}
