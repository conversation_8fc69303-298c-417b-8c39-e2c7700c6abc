import 'package:sales_app/domain/entities/network_connection_status.dart';
import 'package:sales_app/presentation/controllers/network_state.dart';

/// Mapper between domain and presentation network states
/// Follows clean architecture by keeping mapping logic in presentation layer
class NetworkStateMapper {
  const NetworkStateMapper._();

  /// Convert domain entity to presentation state
  static NetworkState fromDomain(NetworkConnectionStatus domainStatus) {
    switch (domainStatus) {
      case NetworkConnectionStatus.connected:
        return const NetworkState.connected();
      case NetworkConnectionStatus.disconnected:
        return const NetworkState.disconnected();
      case NetworkConnectionStatus.unstable:
        return const NetworkState.unstable();
      case NetworkConnectionStatus.checking:
        return const NetworkState.checking();
    }
  }

  /// Convert presentation state to domain entity
  static NetworkConnectionStatus toDomain(NetworkState presentationState) {
    return presentationState.when(
      connected: () => NetworkConnectionStatus.connected,
      disconnected: () => NetworkConnectionStatus.disconnected,
      unstable: () => NetworkConnectionStatus.unstable,
      checking: () => NetworkConnectionStatus.checking,
    );
  }

  /// Batch convert domain stream to presentation stream
  static Stream<NetworkState> mapDomainStream(
    Stream<NetworkConnectionStatus> domainStream,
  ) {
    return domainStream.map(fromDomain);
  }

  /// Get status icon from domain entity (bypassing presentation layer)
  static String getStatusIcon(NetworkConnectionStatus domainStatus) {
    return domainStatus.statusIcon;
  }
}
