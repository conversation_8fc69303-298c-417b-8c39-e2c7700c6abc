// GENERATED CODE - DO NOT MODIFY BY HAND
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'intl/messages_all.dart';

// **************************************************************************
// Generator: Flutter Intl IDE plugin
// Made by Localizely
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, lines_longer_than_80_chars
// ignore_for_file: join_return_with_assignment, prefer_final_in_for_each
// ignore_for_file: avoid_redundant_argument_values, avoid_escaping_inner_quotes

class S {
  S();

  static S? _current;

  static S get current {
    assert(_current != null,
        'No instance of S was loaded. Try to initialize the S delegate before accessing S.current.');
    return _current!;
  }

  static const AppLocalizationDelegate delegate = AppLocalizationDelegate();

  static Future<S> load(Locale locale) {
    final name = (locale.countryCode?.isEmpty ?? false)
        ? locale.languageCode
        : locale.toString();
    final localeName = Intl.canonicalizedLocale(name);
    return initializeMessages(localeName).then((_) {
      Intl.defaultLocale = localeName;
      final instance = S();
      S._current = instance;

      return instance;
    });
  }

  static S of(BuildContext context) {
    final instance = S.maybeOf(context);
    assert(instance != null,
        'No instance of S present in the widget tree. Did you add S.delegate in localizationsDelegates?');
    return instance!;
  }

  static S? maybeOf(BuildContext context) {
    return Localizations.of<S>(context, S);
  }

  /// `KienlongBank Sales App`
  String get appName {
    return Intl.message(
      'KienlongBank Sales App',
      name: 'appName',
      desc: '',
      args: [],
    );
  }

  /// `Home`
  String get home {
    return Intl.message(
      'Home',
      name: 'home',
      desc: '',
      args: [],
    );
  }

  /// `Password`
  String get password {
    return Intl.message(
      'Password',
      name: 'password',
      desc: '',
      args: [],
    );
  }

  /// `Login`
  String get login {
    return Intl.message(
      'Login',
      name: 'login',
      desc: '',
      args: [],
    );
  }

  /// `Introduction`
  String get introduction {
    return Intl.message(
      'Introduction',
      name: 'introduction',
      desc: '',
      args: [],
    );
  }

  /// `Register`
  String get register {
    return Intl.message(
      'Register',
      name: 'register',
      desc: '',
      args: [],
    );
  }

  /// `Register account`
  String get register_account {
    return Intl.message(
      'Register account',
      name: 'register_account',
      desc: '',
      args: [],
    );
  }

  /// `Document Verification Guide`
  String get documentVerificationGuide {
    return Intl.message(
      'Document Verification Guide',
      name: 'documentVerificationGuide',
      desc: '',
      args: [],
    );
  }

  /// `Notes when taking documents:`
  String get notesWhenTakingDocuments {
    return Intl.message(
      'Notes when taking documents:',
      name: 'notesWhenTakingDocuments',
      desc: '',
      args: [],
    );
  }

  /// `Avoid using images like:`
  String get avoidUsingImages {
    return Intl.message(
      'Avoid using images like:',
      name: 'avoidUsingImages',
      desc: '',
      args: [],
    );
  }

  /// `Check front side of ID`
  String get checkFrontSide {
    return Intl.message(
      'Check front side of ID',
      name: 'checkFrontSide',
      desc: '',
      args: [],
    );
  }

  /// `Check back side of ID`
  String get checkBackSide {
    return Intl.message(
      'Check back side of ID',
      name: 'checkBackSide',
      desc: '',
      args: [],
    );
  }

  /// `Please check the photo`
  String get pleaseCheckPhoto {
    return Intl.message(
      'Please check the photo',
      name: 'pleaseCheckPhoto',
      desc: '',
      args: [],
    );
  }

  /// `Please place the document in the frame for verification`
  String get placeDocumentInFrame {
    return Intl.message(
      'Please place the document in the frame for verification',
      name: 'placeDocumentInFrame',
      desc: '',
      args: [],
    );
  }

  /// `Click to capture`
  String get clickToCapture {
    return Intl.message(
      'Click to capture',
      name: 'clickToCapture',
      desc: '',
      args: [],
    );
  }

  /// `Personal Information Confirmation`
  String get personalInfoConfirmation {
    return Intl.message(
      'Personal Information Confirmation',
      name: 'personalInfoConfirmation',
      desc: '',
      args: [],
    );
  }

  /// `Information from ID`
  String get infoFromId {
    return Intl.message(
      'Information from ID',
      name: 'infoFromId',
      desc: '',
      args: [],
    );
  }

  /// `Additional Information`
  String get additionalInfo {
    return Intl.message(
      'Additional Information',
      name: 'additionalInfo',
      desc: '',
      args: [],
    );
  }

  /// `Phone Number`
  String get phoneNumber {
    return Intl.message(
      'Phone Number',
      name: 'phoneNumber',
      desc: '',
      args: [],
    );
  }

  /// `Enter phone number`
  String get enterPhoneNumber {
    return Intl.message(
      'Enter phone number',
      name: 'enterPhoneNumber',
      desc: '',
      args: [],
    );
  }

  /// `Position`
  String get position {
    return Intl.message(
      'Position',
      name: 'position',
      desc: '',
      args: [],
    );
  }

  /// `Province/City`
  String get province {
    return Intl.message(
      'Province/City',
      name: 'province',
      desc: '',
      args: [],
    );
  }

  /// `Select province/city`
  String get selectProvince {
    return Intl.message(
      'Select province/city',
      name: 'selectProvince',
      desc: '',
      args: [],
    );
  }

  /// `Branch`
  String get branch {
    return Intl.message(
      'Branch',
      name: 'branch',
      desc: '',
      args: [],
    );
  }

  /// `Select branch`
  String get selectBranch {
    return Intl.message(
      'Select branch',
      name: 'selectBranch',
      desc: '',
      args: [],
    );
  }

  /// `Branch Address`
  String get branchAddress {
    return Intl.message(
      'Branch Address',
      name: 'branchAddress',
      desc: '',
      args: [],
    );
  }

  /// `Referral Code (if any)`
  String get referralCode {
    return Intl.message(
      'Referral Code (if any)',
      name: 'referralCode',
      desc: '',
      args: [],
    );
  }

  /// `Enter referral code`
  String get enterReferralCode {
    return Intl.message(
      'Enter referral code',
      name: 'enterReferralCode',
      desc: '',
      args: [],
    );
  }

  /// `Registration Successful`
  String get registrationSuccess {
    return Intl.message(
      'Registration Successful',
      name: 'registrationSuccess',
      desc: '',
      args: [],
    );
  }

  /// `KienlongBank will contact you soon to inform the result!`
  String get bankWillContact {
    return Intl.message(
      'KienlongBank will contact you soon to inform the result!',
      name: 'bankWillContact',
      desc: '',
      args: [],
    );
  }

  /// `Close`
  String get close {
    return Intl.message(
      'Close',
      name: 'close',
      desc: '',
      args: [],
    );
  }

  /// `Full Name`
  String get fullName {
    return Intl.message(
      'Full Name',
      name: 'fullName',
      desc: '',
      args: [],
    );
  }

  /// `ID Number`
  String get idNumber {
    return Intl.message(
      'ID Number',
      name: 'idNumber',
      desc: '',
      args: [],
    );
  }

  /// `Residential Address`
  String get residentialAddress {
    return Intl.message(
      'Residential Address',
      name: 'residentialAddress',
      desc: '',
      args: [],
    );
  }

  /// `New CTV`
  String get newCTV {
    return Intl.message(
      'New CTV',
      name: 'newCTV',
      desc: '',
      args: [],
    );
  }

  /// `Daily Installment Loan`
  String get dailyInstallmentLoan {
    return Intl.message(
      'Daily Installment Loan',
      name: 'dailyInstallmentLoan',
      desc: '',
      args: [],
    );
  }

  /// `Benefits as CTV`
  String get benefitsAsCTV {
    return Intl.message(
      'Benefits as CTV',
      name: 'benefitsAsCTV',
      desc: '',
      args: [],
    );
  }

  /// `Steps to become a CTV`
  String get stepsToBecomeCTV {
    return Intl.message(
      'Steps to become a CTV',
      name: 'stepsToBecomeCTV',
      desc: '',
      args: [],
    );
  }

  /// `Daily Installment Loan is a loan product in the form of daily installments (principal, interest) to serve living needs and business activities, other activities.`
  String get dailyInstallmentLoanDesc {
    return Intl.message(
      'Daily Installment Loan is a loan product in the form of daily installments (principal, interest) to serve living needs and business activities, other activities.',
      name: 'dailyInstallmentLoanDesc',
      desc: '',
      args: [],
    );
  }

  /// `Suitable for individuals with sudden, short-term spending needs; small businesses and individual businesses; customers with stable income and good credit history.`
  String get suitableFor {
    return Intl.message(
      'Suitable for individuals with sudden, short-term spending needs; small businesses and individual businesses; customers with stable income and good credit history.',
      name: 'suitableFor',
      desc: '',
      args: [],
    );
  }

  /// `Flexible working hours`
  String get flexibleWorkingHours {
    return Intl.message(
      'Flexible working hours',
      name: 'flexibleWorkingHours',
      desc: '',
      args: [],
    );
  }

  /// `Monthly service fee`
  String get monthlyServiceFee {
    return Intl.message(
      'Monthly service fee',
      name: 'monthlyServiceFee',
      desc: '',
      args: [],
    );
  }

  /// `Unlimited income`
  String get unlimitedIncome {
    return Intl.message(
      'Unlimited income',
      name: 'unlimitedIncome',
      desc: '',
      args: [],
    );
  }

  /// `Work uniform provided`
  String get workUniform {
    return Intl.message(
      'Work uniform provided',
      name: 'workUniform',
      desc: '',
      args: [],
    );
  }

  /// `Health insurance and accident insurance provided by Kienlongbank when meeting outstanding balance conditions`
  String get healthInsurance {
    return Intl.message(
      'Health insurance and accident insurance provided by Kienlongbank when meeting outstanding balance conditions',
      name: 'healthInsurance',
      desc: '',
      args: [],
    );
  }

  /// `Holiday bonus for 30/04, National Day 02/9, ...`
  String get holidayBonus {
    return Intl.message(
      'Holiday bonus for 30/04, National Day 02/9, ...',
      name: 'holidayBonus',
      desc: '',
      args: [],
    );
  }

  /// `Requirements:`
  String get requirements {
    return Intl.message(
      'Requirements:',
      name: 'requirements',
      desc: '',
      args: [],
    );
  }

  /// `Vietnamese citizen with legal capacity, civil act capacity and civil liability according to law`
  String get vietnameseCitizen {
    return Intl.message(
      'Vietnamese citizen with legal capacity, civil act capacity and civil liability according to law',
      name: 'vietnameseCitizen',
      desc: '',
      args: [],
    );
  }

  /// `Good background and character; No criminal record`
  String get goodBackground {
    return Intl.message(
      'Good background and character; No criminal record',
      name: 'goodBackground',
      desc: '',
      args: [],
    );
  }

  /// `Age from 18 to 60, graduated from secondary school or higher`
  String get ageRequirement {
    return Intl.message(
      'Age from 18 to 60, graduated from secondary school or higher',
      name: 'ageRequirement',
      desc: '',
      args: [],
    );
  }

  /// `Good health, clear mind`
  String get healthRequirement {
    return Intl.message(
      'Good health, clear mind',
      name: 'healthRequirement',
      desc: '',
      args: [],
    );
  }

  /// `Minimum collateral of 150 million VND and commitment to comply with Kienlongbank's minimum average outstanding balance regulations`
  String get assetRequirement {
    return Intl.message(
      'Minimum collateral of 150 million VND and commitment to comply with Kienlongbank\'s minimum average outstanding balance regulations',
      name: 'assetRequirement',
      desc: '',
      args: [],
    );
  }

  /// `Understanding of working area`
  String get areaKnowledge {
    return Intl.message(
      'Understanding of working area',
      name: 'areaKnowledge',
      desc: '',
      args: [],
    );
  }

  /// `Not working for other credit institutions or finance companies`
  String get noOtherBank {
    return Intl.message(
      'Not working for other credit institutions or finance companies',
      name: 'noOtherBank',
      desc: '',
      args: [],
    );
  }

  /// `Continue`
  String get continueText {
    return Intl.message(
      'Continue',
      name: 'continueText',
      desc: '',
      args: [],
    );
  }

  /// `Retake`
  String get retake {
    return Intl.message(
      'Retake',
      name: 'retake',
      desc: '',
      args: [],
    );
  }

  /// `Confirm`
  String get confirm {
    return Intl.message(
      'Confirm',
      name: 'confirm',
      desc: '',
      args: [],
    );
  }

  /// `ID Card`
  String get idCard {
    return Intl.message(
      'ID Card',
      name: 'idCard',
      desc: '',
      args: [],
    );
  }

  /// `Passport`
  String get passport {
    return Intl.message(
      'Passport',
      name: 'passport',
      desc: '',
      args: [],
    );
  }

  /// `Server error. Please try again later.`
  String get errorServerInternal {
    return Intl.message(
      'Server error. Please try again later.',
      name: 'errorServerInternal',
      desc: '',
      args: [],
    );
  }

  /// `The requested resource was not found.`
  String get errorServerNotFound {
    return Intl.message(
      'The requested resource was not found.',
      name: 'errorServerNotFound',
      desc: '',
      args: [],
    );
  }

  /// `Too many requests. Please try again later.`
  String get errorServerTooManyRequests {
    return Intl.message(
      'Too many requests. Please try again later.',
      name: 'errorServerTooManyRequests',
      desc: '',
      args: [],
    );
  }

  /// `Invalid request data.`
  String get errorServerBadRequest {
    return Intl.message(
      'Invalid request data.',
      name: 'errorServerBadRequest',
      desc: '',
      args: [],
    );
  }

  /// `An error occurred. Please try again.`
  String get errorServerUnknown {
    return Intl.message(
      'An error occurred. Please try again.',
      name: 'errorServerUnknown',
      desc: '',
      args: [],
    );
  }

  /// `Connection timeout. Please check your internet connection.`
  String get errorNetworkConnectionTimeout {
    return Intl.message(
      'Connection timeout. Please check your internet connection.',
      name: 'errorNetworkConnectionTimeout',
      desc: '',
      args: [],
    );
  }

  /// `Request timeout. Please try again.`
  String get errorNetworkSendTimeout {
    return Intl.message(
      'Request timeout. Please try again.',
      name: 'errorNetworkSendTimeout',
      desc: '',
      args: [],
    );
  }

  /// `Response timeout. Please try again.`
  String get errorNetworkReceiveTimeout {
    return Intl.message(
      'Response timeout. Please try again.',
      name: 'errorNetworkReceiveTimeout',
      desc: '',
      args: [],
    );
  }

  /// `No internet connection. Please check your network settings.`
  String get errorNetworkNoConnection {
    return Intl.message(
      'No internet connection. Please check your network settings.',
      name: 'errorNetworkNoConnection',
      desc: '',
      args: [],
    );
  }

  /// `Request was cancelled.`
  String get errorNetworkRequestCancelled {
    return Intl.message(
      'Request was cancelled.',
      name: 'errorNetworkRequestCancelled',
      desc: '',
      args: [],
    );
  }

  /// `Certificate verification failed.`
  String get errorNetworkCertificate {
    return Intl.message(
      'Certificate verification failed.',
      name: 'errorNetworkCertificate',
      desc: '',
      args: [],
    );
  }

  /// `Network error. Please try again.`
  String get errorNetworkUnknown {
    return Intl.message(
      'Network error. Please try again.',
      name: 'errorNetworkUnknown',
      desc: '',
      args: [],
    );
  }

  /// `Failed to read cached data.`
  String get errorCacheRead {
    return Intl.message(
      'Failed to read cached data.',
      name: 'errorCacheRead',
      desc: '',
      args: [],
    );
  }

  /// `Failed to save data to cache.`
  String get errorCacheWrite {
    return Intl.message(
      'Failed to save data to cache.',
      name: 'errorCacheWrite',
      desc: '',
      args: [],
    );
  }

  /// `Cached data is corrupted.`
  String get errorCacheDataCorrupted {
    return Intl.message(
      'Cached data is corrupted.',
      name: 'errorCacheDataCorrupted',
      desc: '',
      args: [],
    );
  }

  /// `Storage is full.`
  String get errorCacheStorageFull {
    return Intl.message(
      'Storage is full.',
      name: 'errorCacheStorageFull',
      desc: '',
      args: [],
    );
  }

  /// `Cache error occurred.`
  String get errorCacheUnknown {
    return Intl.message(
      'Cache error occurred.',
      name: 'errorCacheUnknown',
      desc: '',
      args: [],
    );
  }

  /// `Authentication failed. Please login again.`
  String get errorAuthInvalidCredentials {
    return Intl.message(
      'Authentication failed. Please login again.',
      name: 'errorAuthInvalidCredentials',
      desc: '',
      args: [],
    );
  }

  /// `Access denied. You don't have permission to perform this action.`
  String get errorAuthAccessDenied {
    return Intl.message(
      'Access denied. You don\'t have permission to perform this action.',
      name: 'errorAuthAccessDenied',
      desc: '',
      args: [],
    );
  }

  /// `Session expired. Please login again.`
  String get errorAuthTokenExpired {
    return Intl.message(
      'Session expired. Please login again.',
      name: 'errorAuthTokenExpired',
      desc: '',
      args: [],
    );
  }

  /// `User not found.`
  String get errorAuthUserNotFound {
    return Intl.message(
      'User not found.',
      name: 'errorAuthUserNotFound',
      desc: '',
      args: [],
    );
  }

  /// `Account is locked or disabled.`
  String get errorAuthAccountLocked {
    return Intl.message(
      'Account is locked or disabled.',
      name: 'errorAuthAccountLocked',
      desc: '',
      args: [],
    );
  }

  /// `Authentication error occurred.`
  String get errorAuthUnknown {
    return Intl.message(
      'Authentication error occurred.',
      name: 'errorAuthUnknown',
      desc: '',
      args: [],
    );
  }

  /// `Please enter a valid email address.`
  String get errorValidationInvalidEmail {
    return Intl.message(
      'Please enter a valid email address.',
      name: 'errorValidationInvalidEmail',
      desc: '',
      args: [],
    );
  }

  /// `Please enter a valid password.`
  String get errorValidationInvalidPassword {
    return Intl.message(
      'Please enter a valid password.',
      name: 'errorValidationInvalidPassword',
      desc: '',
      args: [],
    );
  }

  /// `This field is required.`
  String get errorValidationRequiredField {
    return Intl.message(
      'This field is required.',
      name: 'errorValidationRequiredField',
      desc: '',
      args: [],
    );
  }

  /// `Input is too short.`
  String get errorValidationTooShort {
    return Intl.message(
      'Input is too short.',
      name: 'errorValidationTooShort',
      desc: '',
      args: [],
    );
  }

  /// `Input is too long.`
  String get errorValidationTooLong {
    return Intl.message(
      'Input is too long.',
      name: 'errorValidationTooLong',
      desc: '',
      args: [],
    );
  }

  /// `Invalid format.`
  String get errorValidationInvalidFormat {
    return Intl.message(
      'Invalid format.',
      name: 'errorValidationInvalidFormat',
      desc: '',
      args: [],
    );
  }

  /// `Validation failed.`
  String get errorValidationServer {
    return Intl.message(
      'Validation failed.',
      name: 'errorValidationServer',
      desc: '',
      args: [],
    );
  }

  /// `Validation error occurred.`
  String get errorValidationUnknown {
    return Intl.message(
      'Validation error occurred.',
      name: 'errorValidationUnknown',
      desc: '',
      args: [],
    );
  }
}

class AppLocalizationDelegate extends LocalizationsDelegate<S> {
  const AppLocalizationDelegate();

  List<Locale> get supportedLocales {
    return const <Locale>[
      Locale.fromSubtags(languageCode: 'en'),
      Locale.fromSubtags(languageCode: 'vi'),
    ];
  }

  @override
  bool isSupported(Locale locale) => _isSupported(locale);
  @override
  Future<S> load(Locale locale) => S.load(locale);
  @override
  bool shouldReload(AppLocalizationDelegate old) => false;

  bool _isSupported(Locale locale) {
    for (var supportedLocale in supportedLocales) {
      if (supportedLocale.languageCode == locale.languageCode) {
        return true;
      }
    }
    return false;
  }
}
