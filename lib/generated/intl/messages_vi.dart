// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a vi locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'vi';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
    "additionalInfo": MessageLookupByLibrary.simpleMessage("Thông tin bổ sung"),
    "ageRequirement": MessageLookupByLibrary.simpleMessage(
      "Độ tuổi từ 18 đến 60, tốt nghiệp THCS trở lên",
    ),
    "appName": MessageLookupByLibrary.simpleMessage(
      "Ứng dụng KienlongBank Sales",
    ),
    "areaKnowledge": MessageLookupByLibrary.simpleMessage(
      "Hiểu biết về khu vực hoạt động",
    ),
    "assetRequirement": MessageLookupByLibrary.simpleMessage(
      "Có tài sản đảm bảo tối thiểu 150 triệu đồng và cam kết tuân thủ quy định về dư nợ bình quân tối thiểu của Kienlongbank",
    ),
    "avoidUsingImages": MessageLookupByLibrary.simpleMessage(
      "Tránh sử dụng hình ảnh như:",
    ),
    "bankWillContact": MessageLookupByLibrary.simpleMessage(
      "KienlongBank sẽ liên hệ với bạn trong thời gian sớm nhất để thông báo kết quả!",
    ),
    "benefitsAsCTV": MessageLookupByLibrary.simpleMessage(
      "Quyền lợi khi làm CTV",
    ),
    "branch": MessageLookupByLibrary.simpleMessage("Chi nhánh"),
    "branchAddress": MessageLookupByLibrary.simpleMessage("Địa chỉ chi nhánh"),
    "checkBackSide": MessageLookupByLibrary.simpleMessage(
      "Kiểm tra mặt sau CMND",
    ),
    "checkFrontSide": MessageLookupByLibrary.simpleMessage(
      "Kiểm tra mặt trước CMND",
    ),
    "clickToCapture": MessageLookupByLibrary.simpleMessage("Nhấn để chụp"),
    "close": MessageLookupByLibrary.simpleMessage("Đóng"),
    "confirm": MessageLookupByLibrary.simpleMessage("Xác nhận"),
    "continueText": MessageLookupByLibrary.simpleMessage("Tiếp tục"),
    "dailyInstallmentLoan": MessageLookupByLibrary.simpleMessage(
      "Vay trả góp ngày",
    ),
    "dailyInstallmentLoanDesc": MessageLookupByLibrary.simpleMessage(
      "Vay trả góp ngày là sản phẩm cho vay dưới hình thức trả góp theo ngày (gốc, lãi) để phục vụ nhu cầu sinh hoạt và hoạt động kinh doanh, các hoạt động khác.",
    ),
    "documentVerificationGuide": MessageLookupByLibrary.simpleMessage(
      "Hướng dẫn xác minh giấy tờ",
    ),
    "enterPhoneNumber": MessageLookupByLibrary.simpleMessage(
      "Nhập số điện thoại",
    ),
    "enterReferralCode": MessageLookupByLibrary.simpleMessage(
      "Nhập mã giới thiệu",
    ),
    "errorAuthAccessDenied": MessageLookupByLibrary.simpleMessage(
      "Truy cập bị từ chối. Bạn không có quyền thực hiện hành động này.",
    ),
    "errorAuthAccountLocked": MessageLookupByLibrary.simpleMessage(
      "Tài khoản đã bị khóa hoặc vô hiệu hóa.",
    ),
    "errorAuthInvalidCredentials": MessageLookupByLibrary.simpleMessage(
      "Xác thực thất bại. Vui lòng đăng nhập lại.",
    ),
    "errorAuthTokenExpired": MessageLookupByLibrary.simpleMessage(
      "Phiên đăng nhập đã hết hạn. Vui lòng đăng nhập lại.",
    ),
    "errorAuthUnknown": MessageLookupByLibrary.simpleMessage(
      "Đã xảy ra lỗi xác thực.",
    ),
    "errorAuthUserNotFound": MessageLookupByLibrary.simpleMessage(
      "Không tìm thấy người dùng.",
    ),
    "errorCacheDataCorrupted": MessageLookupByLibrary.simpleMessage(
      "Dữ liệu đã lưu bị hỏng.",
    ),
    "errorCacheRead": MessageLookupByLibrary.simpleMessage(
      "Không thể đọc dữ liệu đã lưu.",
    ),
    "errorCacheStorageFull": MessageLookupByLibrary.simpleMessage(
      "Bộ nhớ đã đầy.",
    ),
    "errorCacheUnknown": MessageLookupByLibrary.simpleMessage(
      "Đã xảy ra lỗi bộ nhớ đệm.",
    ),
    "errorCacheWrite": MessageLookupByLibrary.simpleMessage(
      "Không thể lưu dữ liệu vào bộ nhớ đệm.",
    ),
    "errorNetworkCertificate": MessageLookupByLibrary.simpleMessage(
      "Xác minh chứng chỉ thất bại.",
    ),
    "errorNetworkConnectionTimeout": MessageLookupByLibrary.simpleMessage(
      "Hết thời gian kết nối. Vui lòng kiểm tra kết nối internet.",
    ),
    "errorNetworkNoConnection": MessageLookupByLibrary.simpleMessage(
      "Không có kết nối internet. Vui lòng kiểm tra cài đặt mạng.",
    ),
    "errorNetworkReceiveTimeout": MessageLookupByLibrary.simpleMessage(
      "Hết thời gian nhận phản hồi. Vui lòng thử lại.",
    ),
    "errorNetworkRequestCancelled": MessageLookupByLibrary.simpleMessage(
      "Yêu cầu đã bị hủy.",
    ),
    "errorNetworkSendTimeout": MessageLookupByLibrary.simpleMessage(
      "Hết thời gian gửi yêu cầu. Vui lòng thử lại.",
    ),
    "errorNetworkUnknown": MessageLookupByLibrary.simpleMessage(
      "Lỗi mạng. Vui lòng thử lại.",
    ),
    "errorServerBadRequest": MessageLookupByLibrary.simpleMessage(
      "Dữ liệu yêu cầu không hợp lệ.",
    ),
    "errorServerInternal": MessageLookupByLibrary.simpleMessage(
      "Lỗi máy chủ. Vui lòng thử lại sau.",
    ),
    "errorServerNotFound": MessageLookupByLibrary.simpleMessage(
      "Không tìm thấy tài nguyên được yêu cầu.",
    ),
    "errorServerTooManyRequests": MessageLookupByLibrary.simpleMessage(
      "Quá nhiều yêu cầu. Vui lòng thử lại sau.",
    ),
    "errorServerUnknown": MessageLookupByLibrary.simpleMessage(
      "Đã xảy ra lỗi. Vui lòng thử lại.",
    ),
    "errorValidationInvalidEmail": MessageLookupByLibrary.simpleMessage(
      "Vui lòng nhập địa chỉ email hợp lệ.",
    ),
    "errorValidationInvalidFormat": MessageLookupByLibrary.simpleMessage(
      "Định dạng không hợp lệ.",
    ),
    "errorValidationInvalidPassword": MessageLookupByLibrary.simpleMessage(
      "Vui lòng nhập mật khẩu hợp lệ.",
    ),
    "errorValidationRequiredField": MessageLookupByLibrary.simpleMessage(
      "Trường này là bắt buộc.",
    ),
    "errorValidationServer": MessageLookupByLibrary.simpleMessage(
      "Xác thực thất bại.",
    ),
    "errorValidationTooLong": MessageLookupByLibrary.simpleMessage(
      "Dữ liệu nhập quá dài.",
    ),
    "errorValidationTooShort": MessageLookupByLibrary.simpleMessage(
      "Dữ liệu nhập quá ngắn.",
    ),
    "errorValidationUnknown": MessageLookupByLibrary.simpleMessage(
      "Đã xảy ra lỗi xác thực.",
    ),
    "flexibleWorkingHours": MessageLookupByLibrary.simpleMessage(
      "Thời gian làm việc linh hoạt",
    ),
    "fullName": MessageLookupByLibrary.simpleMessage("Họ và tên"),
    "goodBackground": MessageLookupByLibrary.simpleMessage(
      "Có lý lịch rõ ràng, phẩm chất đạo đức tốt; Không có tiền án, tiền sự",
    ),
    "healthInsurance": MessageLookupByLibrary.simpleMessage(
      "Được Kienlongbank cấp bảo hiểm sức khỏe và bảo hiểm tai nạn khi đạt điều kiện dư nợ",
    ),
    "healthRequirement": MessageLookupByLibrary.simpleMessage(
      "Có sức khỏe tốt, minh mẫn",
    ),
    "holidayBonus": MessageLookupByLibrary.simpleMessage(
      "Thưởng lễ 30/04, Quốc khánh 02/9, ...",
    ),
    "home": MessageLookupByLibrary.simpleMessage("Trang chủ"),
    "idCard": MessageLookupByLibrary.simpleMessage("CMND/CCCD"),
    "idNumber": MessageLookupByLibrary.simpleMessage("Số CMND"),
    "infoFromId": MessageLookupByLibrary.simpleMessage("Thông tin từ CMND"),
    "introduction": MessageLookupByLibrary.simpleMessage("Giới thiệu"),
    "login": MessageLookupByLibrary.simpleMessage("Đăng nhập"),
    "monthlyServiceFee": MessageLookupByLibrary.simpleMessage(
      "Phí dịch vụ hàng tháng",
    ),
    "networkCheck": MessageLookupByLibrary.simpleMessage("Kiểm tra"),
    "networkChecking": MessageLookupByLibrary.simpleMessage(
      "Đang kiểm tra kết nối...",
    ),
    "networkCheckingConnection": MessageLookupByLibrary.simpleMessage(
      "Đang kiểm tra kết nối...",
    ),
    "networkCheckingConnectionDescription":
        MessageLookupByLibrary.simpleMessage(
          "Đang kiểm tra trạng thái kết nối...",
        ),
    "networkConnected": MessageLookupByLibrary.simpleMessage("Đã kết nối"),
    "networkContinue": MessageLookupByLibrary.simpleMessage("Tiếp tục"),
    "networkDisconnected": MessageLookupByLibrary.simpleMessage(
      "Mất kết nối mạng",
    ),
    "networkDisconnectedDescription": MessageLookupByLibrary.simpleMessage(
      "Vui lòng kiểm tra kết nối internet và thử lại",
    ),
    "networkReconnectedSuccess": MessageLookupByLibrary.simpleMessage(
      "Đã kết nối lại thành công",
    ),
    "networkRetry": MessageLookupByLibrary.simpleMessage("Thử lại"),
    "networkRetryChecking": MessageLookupByLibrary.simpleMessage(
      "Đang kiểm tra...",
    ),
    "networkUnstable": MessageLookupByLibrary.simpleMessage(
      "Kết nối không ổn định",
    ),
    "networkUnstableConnectionDescription":
        MessageLookupByLibrary.simpleMessage(
          "Kết nối mạng không ổn định. Một số tính năng có thể bị ảnh hưởng",
        ),
    "networkUnstableDescription": MessageLookupByLibrary.simpleMessage(
      "Mạng không ổn định - Một số tính năng có thể bị ảnh hưởng",
    ),
    "networkUnstableWarning": MessageLookupByLibrary.simpleMessage(
      "Kết nối không ổn định",
    ),
    "newCTV": MessageLookupByLibrary.simpleMessage("CTV mới"),
    "noOtherBank": MessageLookupByLibrary.simpleMessage(
      "Không làm việc cho các tổ chức tín dụng hoặc công ty tài chính khác",
    ),
    "notesWhenTakingDocuments": MessageLookupByLibrary.simpleMessage(
      "Lưu ý khi chụp giấy tờ:",
    ),
    "passport": MessageLookupByLibrary.simpleMessage("Hộ chiếu"),
    "password": MessageLookupByLibrary.simpleMessage("Mật khẩu"),
    "personalInfoConfirmation": MessageLookupByLibrary.simpleMessage(
      "Xác nhận thông tin cá nhân",
    ),
    "phoneNumber": MessageLookupByLibrary.simpleMessage("Số điện thoại"),
    "placeDocumentInFrame": MessageLookupByLibrary.simpleMessage(
      "Vui lòng đặt giấy tờ vào khung để xác minh",
    ),
    "pleaseCheckPhoto": MessageLookupByLibrary.simpleMessage(
      "Vui lòng kiểm tra ảnh",
    ),
    "position": MessageLookupByLibrary.simpleMessage("Chức vụ"),
    "province": MessageLookupByLibrary.simpleMessage("Tỉnh/Thành phố"),
    "referralCode": MessageLookupByLibrary.simpleMessage(
      "Mã giới thiệu (nếu có)",
    ),
    "register": MessageLookupByLibrary.simpleMessage("Đăng ký"),
    "register_account": MessageLookupByLibrary.simpleMessage(
      "Đăng ký tài khoản",
    ),
    "registrationSuccess": MessageLookupByLibrary.simpleMessage(
      "Đăng ký thành công",
    ),
    "requirements": MessageLookupByLibrary.simpleMessage("Yêu cầu:"),
    "residentialAddress": MessageLookupByLibrary.simpleMessage(
      "Địa chỉ thường trú",
    ),
    "retake": MessageLookupByLibrary.simpleMessage("Chụp lại"),
    "selectBranch": MessageLookupByLibrary.simpleMessage("Chọn chi nhánh"),
    "selectProvince": MessageLookupByLibrary.simpleMessage(
      "Chọn tỉnh/thành phố",
    ),
    "stepsToBecomeCTV": MessageLookupByLibrary.simpleMessage(
      "Các bước trở thành CTV",
    ),
    "suitableFor": MessageLookupByLibrary.simpleMessage(
      "Phù hợp với cá nhân có nhu cầu chi tiêu đột xuất, ngắn hạn; hộ kinh doanh và doanh nghiệp cá thể; khách hàng có thu nhập ổn định và lịch sử tín dụng tốt.",
    ),
    "unlimitedIncome": MessageLookupByLibrary.simpleMessage(
      "Thu nhập không giới hạn",
    ),
    "vietnameseCitizen": MessageLookupByLibrary.simpleMessage(
      "Là công dân Việt Nam có năng lực pháp luật dân sự, năng lực hành vi dân sự và chịu trách nhiệm dân sự theo quy định của pháp luật",
    ),
    "workUniform": MessageLookupByLibrary.simpleMessage(
      "Được cấp đồng phục làm việc",
    ),
  };
}
