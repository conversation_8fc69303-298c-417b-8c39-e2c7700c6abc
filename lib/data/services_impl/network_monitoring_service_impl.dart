import 'dart:async';
import 'package:injectable/injectable.dart';
import 'package:sales_app/core/utils/app_logger.dart';
import 'package:sales_app/domain/services/network_info.dart';
import 'package:sales_app/domain/services/network_monitoring_service.dart';
import 'package:sales_app/presentation/controllers/network_state.dart';

/// Implementation of network monitoring service
/// Handles business logic for network state tracking and stability analysis
@Injectable(as: NetworkMonitoringService)
class NetworkMonitoringServiceImpl implements NetworkMonitoringService {
  final NetworkInfo _networkInfo;
  final NetworkMonitoringConfig _config;
  
  // State management
  final StreamController<NetworkState> _stateController = StreamController<NetworkState>.broadcast();
  NetworkState _currentState = const NetworkState.checking();
  
  // Connectivity tracking
  StreamSubscription<bool>? _connectivitySubscription;
  Timer? _unstableTimer;
  
  // Stability tracking
  int _disconnectionCount = 0;
  DateTime? _lastDisconnection;
  
  NetworkMonitoringServiceImpl(this._networkInfo)
      : _config = NetworkMonitoringConfig.defaultConfig;

  @override
  Stream<NetworkState> get networkStateStream => _stateController.stream;

  @override
  NetworkState get currentState => _currentState;

  @override
  Future<void> initialize() async {
    AppLogger.info('Initializing network monitoring service');
    
    // Check initial connection status
    await _checkInitialConnection();
    
    // Start listening to connectivity changes
    _startConnectivityListener();
  }

  @override
  Future<void> checkConnection() async {
    _updateState(const NetworkState.checking());
    
    // Add small delay for better UX
    await Future.delayed(_config.checkDelay);
    
    try {
      final isConnected = await _networkInfo.isConnected;
      _updateState(isConnected 
          ? const NetworkState.connected() 
          : const NetworkState.disconnected());
      
      AppLogger.info('Manual connection check completed', data: {
        'isConnected': isConnected,
      });
    } catch (e) {
      AppLogger.error('Error during manual connection check', error: e);
      _updateState(const NetworkState.disconnected());
    }
  }

  @override
  void forceConnected() {
    _resetStabilityTracking();
    _updateState(const NetworkState.connected());
    AppLogger.info('Network state forced to connected', data: {
      'reason': 'manual_override',
      'timestamp': DateTime.now().toIso8601String(),
    });
  }

  @override
  void handleConnectivityChange(bool isConnected) {
    AppLogger.info('Network connectivity changed', data: {
      'isConnected': isConnected,
      'previousState': _currentState.toString(),
      'timestamp': DateTime.now().toIso8601String(),
    });

    if (isConnected) {
      _handleReconnection();
    } else {
      _handleDisconnection();
    }
  }

  @override
  void dispose() {
    AppLogger.info('Disposing network monitoring service');
    _connectivitySubscription?.cancel();
    _unstableTimer?.cancel();
    _stateController.close();
  }

  /// Check initial connection status
  Future<void> _checkInitialConnection() async {
    try {
      final isConnected = await _networkInfo.isConnected;
      _updateState(isConnected 
          ? const NetworkState.connected() 
          : const NetworkState.disconnected());
      
      AppLogger.info('Initial network status', data: {
        'isConnected': isConnected,
        'state': _currentState.toString(),
      });
    } catch (e) {
      AppLogger.error('Error checking initial connection', error: e);
      _updateState(const NetworkState.disconnected());
    }
  }

  /// Start listening to connectivity changes
  void _startConnectivityListener() {
    _connectivitySubscription?.cancel();
    
    _connectivitySubscription = _networkInfo.connectivityStream.listen(
      handleConnectivityChange,
      onError: (error) {
        AppLogger.error('Connectivity stream error', error: error);
        _updateState(const NetworkState.disconnected());
      },
    );
  }

  /// Handle reconnection
  void _handleReconnection() {
    // Cancel unstable timer if running
    _unstableTimer?.cancel();
    
    // Check if this was part of unstable pattern
    if (_isConnectionUnstable()) {
      _updateState(const NetworkState.unstable());
      AppLogger.warning('Connection marked as unstable', data: {
        'disconnectionCount': _disconnectionCount,
        'timeWindow': _config.unstableWindow.inMinutes,
      });
      
      // Clear unstable state after some time of stable connection
      _unstableTimer = Timer(_config.stabilizationDuration, () {
        _resetStabilityTracking();
        _updateState(const NetworkState.connected());
        AppLogger.info('Connection stabilized, returning to normal state');
      });
    } else {
      _updateState(const NetworkState.connected());
    }
  }

  /// Handle disconnection
  void _handleDisconnection() {
    _updateState(const NetworkState.disconnected());
    _trackDisconnection();
  }

  /// Track disconnection for stability analysis
  void _trackDisconnection() {
    final now = DateTime.now();
    
    // Reset count if outside the unstable window
    if (_lastDisconnection != null && 
        now.difference(_lastDisconnection!) > _config.unstableWindow) {
      _disconnectionCount = 0;
    }
    
    _disconnectionCount++;
    _lastDisconnection = now;
    
    AppLogger.info('Disconnection tracked', data: {
      'count': _disconnectionCount,
      'timestamp': now.toIso8601String(),
    });
  }

  /// Check if connection is unstable
  bool _isConnectionUnstable() {
    return _disconnectionCount >= _config.unstableThreshold;
  }

  /// Reset stability tracking
  void _resetStabilityTracking() {
    _disconnectionCount = 0;
    _lastDisconnection = null;
    _unstableTimer?.cancel();
  }

  /// Update state and notify listeners
  void _updateState(NetworkState newState) {
    _currentState = newState;
    _stateController.add(newState);
  }
}
