import 'dart:async';
import 'package:injectable/injectable.dart';
import 'package:sales_app/core/utils/app_logger.dart';
import 'package:sales_app/domain/entities/network_connection_status.dart';
import 'package:sales_app/domain/services/network_info.dart';
import 'package:sales_app/domain/services/network_monitoring_service.dart';

/// Implementation of network monitoring service
/// Handles business logic for network state tracking and stability analysis
@Injectable(as: NetworkMonitoringService)
class NetworkMonitoringServiceImpl implements NetworkMonitoringService {
  final NetworkInfo _networkInfo;
  final NetworkMonitoringConfig _config;
  
  // State management
  final StreamController<NetworkConnectionStatus> _stateController = StreamController<NetworkConnectionStatus>.broadcast();
  NetworkConnectionStatus _currentStatus = NetworkConnectionStatus.checking;

  // Connectivity tracking
  StreamSubscription<bool>? _connectivitySubscription;
  Timer? _unstableTimer;

  // Stability tracking
  int _disconnectionCount = 0;
  DateTime? _lastDisconnection;

  NetworkMonitoringServiceImpl(this._networkInfo)
      : _config = NetworkMonitoringConfig.defaultConfig;

  @override
  Stream<NetworkConnectionStatus> get connectionStatusStream => _stateController.stream;

  @override
  NetworkConnectionStatus get currentStatus => _currentStatus;

  @override
  Future<void> initialize() async {
    AppLogger.info('Initializing network monitoring service');
    
    // Check initial connection status
    await _checkInitialConnection();
    
    // Start listening to connectivity changes
    _startConnectivityListener();
  }

  @override
  Future<void> checkConnection() async {
    _updateState(NetworkConnectionStatus.checking);

    // Add small delay for better UX
    await Future.delayed(_config.checkDelay);

    try {
      final isConnected = await _networkInfo.isConnected;
      _updateState(isConnected
          ? NetworkConnectionStatus.connected
          : NetworkConnectionStatus.disconnected);

      AppLogger.info('Manual connection check completed', data: {
        'isConnected': isConnected,
      });
    } catch (e) {
      AppLogger.error('Error during manual connection check', error: e);
      _updateState(NetworkConnectionStatus.disconnected);
    }
  }

  @override
  void forceConnected() {
    _resetStabilityTracking();
    _updateState(NetworkConnectionStatus.connected);
    AppLogger.info('Network state forced to connected', data: {
      'reason': 'manual_override',
      'timestamp': DateTime.now().toIso8601String(),
    });
  }

  @override
  void handleConnectivityChange(bool isConnected) {
    AppLogger.info('Network connectivity changed', data: {
      'isConnected': isConnected,
      'previousState': _currentStatus.toString(),
      'timestamp': DateTime.now().toIso8601String(),
    });

    if (isConnected) {
      _handleReconnection();
    } else {
      _handleDisconnection();
    }
  }

  @override
  void dispose() {
    AppLogger.info('Disposing network monitoring service');
    _connectivitySubscription?.cancel();
    _unstableTimer?.cancel();
    _stateController.close();
  }

  /// Check initial connection status
  Future<void> _checkInitialConnection() async {
    try {
      final isConnected = await _networkInfo.isConnected;
      _updateState(isConnected
          ? NetworkConnectionStatus.connected
          : NetworkConnectionStatus.disconnected);

      AppLogger.info('Initial network status', data: {
        'isConnected': isConnected,
        'state': _currentStatus.toString(),
      });
    } catch (e) {
      AppLogger.error('Error checking initial connection', error: e);
      _updateState(NetworkConnectionStatus.disconnected);
    }
  }

  /// Start listening to connectivity changes
  void _startConnectivityListener() {
    _connectivitySubscription?.cancel();
    
    _connectivitySubscription = _networkInfo.connectivityStream.listen(
      handleConnectivityChange,
      onError: (error) {
        AppLogger.error('Connectivity stream error', error: error);
        _updateState(NetworkConnectionStatus.disconnected);
      },
    );
  }

  /// Handle reconnection
  void _handleReconnection() {
    // Cancel unstable timer if running
    _unstableTimer?.cancel();
    
    // Check if this was part of unstable pattern
    if (_isConnectionUnstable()) {
      _updateState(NetworkConnectionStatus.unstable);
      AppLogger.warning('Connection marked as unstable', data: {
        'disconnectionCount': _disconnectionCount,
        'timeWindow': _config.unstableWindow.inMinutes,
      });

      // Clear unstable state after some time of stable connection
      _unstableTimer = Timer(_config.stabilizationDuration, () {
        _resetStabilityTracking();
        _updateState(NetworkConnectionStatus.connected);
        AppLogger.info('Connection stabilized, returning to normal state');
      });
    } else {
      _updateState(NetworkConnectionStatus.connected);
    }
  }

  /// Handle disconnection
  void _handleDisconnection() {
    _updateState(NetworkConnectionStatus.disconnected);
    _trackDisconnection();
  }

  /// Track disconnection for stability analysis
  void _trackDisconnection() {
    final now = DateTime.now();
    
    // Reset count if outside the unstable window
    if (_lastDisconnection != null && 
        now.difference(_lastDisconnection!) > _config.unstableWindow) {
      _disconnectionCount = 0;
    }
    
    _disconnectionCount++;
    _lastDisconnection = now;
    
    AppLogger.info('Disconnection tracked', data: {
      'count': _disconnectionCount,
      'timestamp': now.toIso8601String(),
    });
  }

  /// Check if connection is unstable
  bool _isConnectionUnstable() {
    return _disconnectionCount >= _config.unstableThreshold;
  }

  /// Reset stability tracking
  void _resetStabilityTracking() {
    _disconnectionCount = 0;
    _lastDisconnection = null;
    _unstableTimer?.cancel();
  }

  /// Update state and notify listeners
  void _updateState(NetworkConnectionStatus newStatus) {
    _currentStatus = newStatus;
    _stateController.add(newStatus);
  }
}
