import 'dart:async';
import 'package:injectable/injectable.dart';
import '../../domain/services/environment_service.dart';
import '../../domain/services/storage_service.dart';
import '../../domain/entities/environment_entity.dart' as env;
import '../../core/utils/app_logger.dart';
import '../../core/constants/environment_constants.dart';

/// Implementation of EnvironmentService
/// Data layer implementation following Clean Architecture
@LazySingleton(as: EnvironmentService)
class EnvironmentServiceImpl implements EnvironmentService {
  final StorageService _storageService;
  env.EnvironmentEntity _currentConfig = _getDefaultConfig();

  // Stream controller for reactive UI updates
  final StreamController<env.EnvironmentEntity> _environmentController =
      StreamController<env.EnvironmentEntity>.broadcast();

  EnvironmentServiceImpl(this._storageService);

  @override
  Stream<env.EnvironmentEntity> get environmentChanges => _environmentController.stream;

  // ✅ Static environment configurations - created once, reused many times
  static const Map<env.Environment, env.EnvironmentEntity> _environmentConfigs = {
    env.Environment.dev: env.EnvironmentEntity(
      environment: env.Environment.dev,
      baseUrl: EnvironmentConstants.devBaseUrl,
      apiVersion: EnvironmentConstants.apiVersion,
      enableLogging: EnvironmentConstants.devLoggingEnabled,
      enableMockApi: EnvironmentConstants.devMockApiEnabled,
      timeoutDuration: EnvironmentConstants.devTimeoutDuration,
    ),
    env.Environment.staging: env.EnvironmentEntity(
      environment: env.Environment.staging,
      baseUrl: EnvironmentConstants.stagingBaseUrl,
      apiVersion: EnvironmentConstants.apiVersion,
      enableLogging: EnvironmentConstants.stagingLoggingEnabled,
      enableMockApi: EnvironmentConstants.stagingMockApiEnabled,
      timeoutDuration: EnvironmentConstants.stagingTimeoutDuration,
    ),
    env.Environment.prod: env.EnvironmentEntity(
      environment: env.Environment.prod,
      baseUrl: EnvironmentConstants.prodBaseUrl,
      apiVersion: EnvironmentConstants.apiVersion,
      enableLogging: EnvironmentConstants.prodLoggingEnabled,
      enableMockApi: EnvironmentConstants.prodMockApiEnabled,
      timeoutDuration: EnvironmentConstants.prodTimeoutDuration,
    ),
  };

  /// Get default environment configuration (Dev)
  static env.EnvironmentEntity _getDefaultConfig() {
    return _environmentConfigs[env.Environment.dev]!;
  }

  /// Get configuration for specific environment - O(1) lookup
  static env.EnvironmentEntity _getConfigForEnvironment(env.Environment environment) {
    return _environmentConfigs[environment]!;
  }

  @override
  env.EnvironmentEntity get currentConfig => _currentConfig;

  @override
  env.Environment get currentEnvironment => _currentConfig.environment;

  @override
  List<env.Environment> get availableEnvironments => env.Environment.values;

  @override
  Future<void> initialize() async {
    await _loadSavedEnvironment();
  }

  /// Load saved environment from storage
  Future<void> _loadSavedEnvironment() async {
    try {
      final savedEnvKey = await _storageService.getSelectedEnvironment();
      if (savedEnvKey != null) {
        final environment = env.Environment.fromKey(savedEnvKey);
        _currentConfig = _getConfigForEnvironment(environment);
        AppLogger.info('Loaded saved environment: ${_currentConfig.environment.displayName}');
      } else {
        AppLogger.info('No saved environment found, using default: ${_currentConfig.environment.displayName}');
      }

      // Notify listeners about the loaded environment
      _environmentController.add(_currentConfig);
    } catch (e) {
      AppLogger.error('Error loading saved environment: $e');
      _currentConfig = _getDefaultConfig(); // Fallback to dev
      _environmentController.add(_currentConfig);
    }
  }

  @override
  Future<void> switchEnvironment(env.Environment environment) async {
    try {
      final newConfig = _getConfigForEnvironment(environment);

      // Save to storage
      await _storageService.saveSelectedEnvironment(environment.key);

      // Update current config
      _currentConfig = newConfig;

      // Notify listeners about the environment change
      _environmentController.add(_currentConfig);

      AppLogger.info('Switched to environment: ${environment.displayName}');
      AppLogger.info('New base URL: ${newConfig.baseUrl}');

    } catch (e) {
      AppLogger.error('Error switching environment: $e');
      throw Exception('Failed to switch environment: $e');
    }
  }

  @override
  String get baseUrl => _currentConfig.baseUrl;

  @override
  String get fullApiUrl => _currentConfig.fullApiUrl;

  @override
  bool get isLoggingEnabled => _currentConfig.enableLogging;

  @override
  bool get isMockApiEnabled => _currentConfig.enableMockApi;

  @override
  int get timeoutDuration => _currentConfig.timeoutDuration;

  @override
  Future<void> resetToDefault() async {
    await switchEnvironment(env.Environment.dev);
  }

  @override
  String get environmentDisplayInfo {
    final environment = _currentConfig.environment;
    return '${environment.displayName} (${_currentConfig.baseUrl})';
  }

  @override
  bool get isProduction => _currentConfig.environment == env.Environment.prod;

  @override
  bool get isDevelopment => _currentConfig.environment == env.Environment.dev;

  @override
  bool get isStaging => _currentConfig.environment == env.Environment.staging;
}
