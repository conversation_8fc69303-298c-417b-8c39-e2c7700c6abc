import 'dart:async';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:injectable/injectable.dart';
import 'package:sales_app/domain/services/network_info.dart';

@Injectable(as: NetworkInfo)
class NetworkInfoImpl implements NetworkInfo {
  final Connectivity _connectivity;
  StreamController<bool>? _connectivityController;

  NetworkInfoImpl(this._connectivity);

  @override
  Future<bool> get isConnected async {
    final result = await _connectivity.checkConnectivity();
    // checkConnectivity() returns a single ConnectivityResult in newer versions
    return result != ConnectivityResult.none;
  }

  @override
  Stream<bool> get connectivityStream {
    _connectivityController ??= StreamController<bool>.broadcast();

    // Listen to connectivity changes
    _connectivity.onConnectivityChanged.listen((ConnectivityResult result) {
      final isConnected = result != ConnectivityResult.none;
      _connectivityController?.add(isConnected);
    });

    return _connectivityController!.stream;
  }

  void dispose() {
    _connectivityController?.close();
    _connectivityController = null;
  }
}
