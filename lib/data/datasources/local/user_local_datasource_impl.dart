import 'package:injectable/injectable.dart';
import '../../../domain/entities/user.dart';
import '../../../core/utils/app_logger.dart';
import 'user_local_datasource.dart';
import 'database/app_database.dart';
import '../../models/local/user_local_model.dart';

/// Implementation của UserLocalDataSource sử dụng Floor ORM
/// Chịu trách nhiệm convert giữa Domain Entities và Local Models
@LazySingleton(as: UserLocalDataSource)
class UserLocalDataSourceImpl implements UserLocalDataSource {
  AppDatabase? _database;
  
  /// Get database instance
  Future<AppDatabase> get database async {
    _database ??= await AppDatabase.getInstance();
    return _database!;
  }

  // ==================== BASIC CRUD OPERATIONS ====================

  @override
  Future<void> saveUser(User user) async {
    try {
      final db = await database;
      final userLocalModel = UserLocalModel.fromDomain(user);
      await db.userDao.saveUser(userLocalModel);
      
      AppLogger.info('User saved to local database', data: {
        'userId': user.id,
        'email': user.email,
      });
    } catch (e) {
      AppLogger.error('Failed to save user to database', error: e);
      rethrow;
    }
  }

  @override
  Future<User?> getUserById(String id) async {
    try {
      final db = await database;
      final userLocalModel = await db.userDao.getUserById(id);
      
      if (userLocalModel != null) {
        AppLogger.debug('User found in local database', data: {'userId': id});
        return userLocalModel.toDomain();
      }
      
      AppLogger.debug('User not found in local database', data: {'userId': id});
      return null;
    } catch (e) {
      AppLogger.error('Failed to get user from database', error: e);
      return null;
    }
  }

  @override
  Future<User?> getUserByEmail(String email) async {
    try {
      final db = await database;
      final userLocalModel = await db.userDao.getUserByEmail(email);
      
      if (userLocalModel != null) {
        AppLogger.debug('User found by email in local database', data: {'email': email});
        return userLocalModel.toDomain();
      }
      
      AppLogger.debug('User not found by email in local database', data: {'email': email});
      return null;
    } catch (e) {
      AppLogger.error('Failed to get user by email from database', error: e);
      return null;
    }
  }

  @override
  Future<List<User>> getAllUsers() async {
    try {
      final db = await database;
      final userLocalModels = await db.userDao.getAllUsers();
      
      final users = userLocalModels.map((model) => model.toDomain()).toList();
      
      AppLogger.debug('Retrieved all users from local database', data: {
        'count': users.length,
      });
      
      return users;
    } catch (e) {
      AppLogger.error('Failed to get all users from database', error: e);
      return [];
    }
  }

  @override
  Future<void> deleteUser(String id) async {
    try {
      final db = await database;
      await db.userDao.deleteUserById(id);
      
      AppLogger.info('User deleted from local database', data: {'userId': id});
    } catch (e) {
      AppLogger.error('Failed to delete user from database', error: e);
      rethrow;
    }
  }

  @override
  Future<void> deleteAllUsers() async {
    try {
      final db = await database;
      await db.userDao.deleteAllUsers();
      
      AppLogger.info('All users deleted from local database');
    } catch (e) {
      AppLogger.error('Failed to delete all users from database', error: e);
      rethrow;
    }
  }

  @override
  Future<bool> userExists(String id) async {
    try {
      final db = await database;
      final count = await db.userDao.getUserCount(id);
      return (count ?? 0) > 0;
    } catch (e) {
      AppLogger.error('Failed to check if user exists', error: e);
      return false;
    }
  }

  @override
  Future<int> getUsersCount() async {
    try {
      final db = await database;
      final count = await db.userDao.getTotalUsersCount();
      return count ?? 0;
    } catch (e) {
      AppLogger.error('Failed to get users count', error: e);
      return 0;
    }
  }

  // ==================== SYNC OPERATIONS ====================

  @override
  Future<void> saveUserOffline(User user) async {
    try {
      final db = await database;
      final userLocalModel = UserLocalModel.fromDomain(user).copyWith(
        syncStatus: 'pending_sync',
        updatedAt: DateTime.now().millisecondsSinceEpoch,
      );
      await db.userDao.saveUserOffline(userLocalModel);
      
      AppLogger.info('User saved offline (pending sync)', data: {
        'userId': user.id,
        'email': user.email,
      });
    } catch (e) {
      AppLogger.error('Failed to save user offline', error: e);
      rethrow;
    }
  }

  @override
  Future<List<User>> getPendingSyncUsers() async {
    try {
      final db = await database;
      final userLocalModels = await db.userDao.getPendingSyncUsers();
      
      final users = userLocalModels.map((model) => model.toDomain()).toList();
      
      AppLogger.debug('Retrieved pending sync users', data: {
        'count': users.length,
      });
      
      return users;
    } catch (e) {
      AppLogger.error('Failed to get pending sync users', error: e);
      return [];
    }
  }

  @override
  Future<List<User>> getFailedSyncUsers() async {
    try {
      final db = await database;
      final userLocalModels = await db.userDao.getFailedSyncUsers();
      
      final users = userLocalModels.map((model) => model.toDomain()).toList();
      
      AppLogger.debug('Retrieved failed sync users', data: {
        'count': users.length,
      });
      
      return users;
    } catch (e) {
      AppLogger.error('Failed to get failed sync users', error: e);
      return [];
    }
  }

  @override
  Future<void> markUserAsSynced(String userId, {int? serverTimestamp}) async {
    try {
      final db = await database;
      final timestamp = serverTimestamp ?? DateTime.now().millisecondsSinceEpoch;
      await db.userDao.markUserAsSynced(userId, timestamp);
      
      AppLogger.info('User marked as synced', data: {
        'userId': userId,
        'serverTimestamp': timestamp,
      });
    } catch (e) {
      AppLogger.error('Failed to mark user as synced', error: e);
      rethrow;
    }
  }

  @override
  Future<void> markUserSyncFailed(String userId) async {
    try {
      final db = await database;
      final now = DateTime.now().millisecondsSinceEpoch;
      await db.userDao.updateSyncStatus(userId, 'sync_failed', now);
      
      AppLogger.warning('User sync marked as failed', data: {'userId': userId});
    } catch (e) {
      AppLogger.error('Failed to mark user sync as failed', error: e);
      rethrow;
    }
  }

  // ==================== UTILITY OPERATIONS ====================

  @override
  Future<List<User>> getUsersCreatedAfter(int timestamp) async {
    try {
      final db = await database;
      final userLocalModels = await db.userDao.getUsersCreatedAfter(timestamp);
      
      return userLocalModels.map((model) => model.toDomain()).toList();
    } catch (e) {
      AppLogger.error('Failed to get users created after timestamp', error: e);
      return [];
    }
  }

  @override
  Future<List<User>> getUsersUpdatedAfter(int timestamp) async {
    try {
      final db = await database;
      final userLocalModels = await db.userDao.getUsersUpdatedAfter(timestamp);
      
      return userLocalModels.map((model) => model.toDomain()).toList();
    } catch (e) {
      AppLogger.error('Failed to get users updated after timestamp', error: e);
      return [];
    }
  }

  @override
  Future<Map<String, dynamic>> getDatabaseInfo() async {
    try {
      final db = await database;
      return await db.getDatabaseInfo();
    } catch (e) {
      AppLogger.error('Failed to get database info', error: e);
      return {};
    }
  }
}
