import 'package:floor/floor.dart';
import '../../../models/local/user_local_model.dart';

/// Data Access Object cho User operations
/// Đ<PERSON>nh nghĩa các database operations cho User table
/// DAO chỉ làm việc với Local Models, không biết về Domain Entities
@dao
abstract class UserDao {
  
  // ==================== BASIC CRUD OPERATIONS ====================
  
  /// Get user by ID
  @Query('SELECT * FROM UserLocalModel WHERE id = :id')
  Future<UserLocalModel?> getUserById(String id);

  /// Get user by email (for login)
  @Query('SELECT * FROM UserLocalModel WHERE email = :email')
  Future<UserLocalModel?> getUserByEmail(String email);

  /// Get all users
  @Query('SELECT * FROM UserLocalModel ORDER BY updatedAt DESC')
  Future<List<UserLocalModel>> getAllUsers();
  
  /// Insert user
  @insert
  Future<void> insertUser(UserLocalModel user);
  
  /// Update user
  @update
  Future<void> updateUser(UserLocalModel user);
  
  /// Delete user by ID
  @Query('DELETE FROM UserLocalModel WHERE id = :id')
  Future<void> deleteUserById(String id);

  /// Delete all users (for logout/clear data)
  @Query('DELETE FROM UserLocalModel')
  Future<void> deleteAllUsers();
  
  // ==================== SYNC OPERATIONS ====================
  
  /// Get users that need to be synced to server
  @Query('SELECT * FROM UserLocalModel WHERE syncStatus = :status')
  Future<List<UserLocalModel>> getUsersBySyncStatus(String status);

  /// Get users pending sync
  @Query('SELECT * FROM UserLocalModel WHERE syncStatus = "pending_sync"')
  Future<List<UserLocalModel>> getPendingSyncUsers();

  /// Get users with failed sync
  @Query('SELECT * FROM UserLocalModel WHERE syncStatus = "sync_failed"')
  Future<List<UserLocalModel>> getFailedSyncUsers();
  
  /// Update sync status for a user
  @Query('UPDATE UserLocalModel SET syncStatus = :status, updatedAt = :timestamp WHERE id = :id')
  Future<void> updateSyncStatus(String id, String status, int timestamp);

  /// Mark user as synced with server timestamp
  @Query('UPDATE UserLocalModel SET syncStatus = "synced", serverUpdatedAt = :serverTimestamp WHERE id = :id')
  Future<void> markUserAsSynced(String id, int serverTimestamp);
  
  // ==================== UTILITY OPERATIONS ====================
  
  /// Check if user exists
  @Query('SELECT COUNT(*) FROM UserLocalModel WHERE id = :id')
  Future<int?> getUserCount(String id);

  /// Get total users count
  @Query('SELECT COUNT(*) FROM UserLocalModel')
  Future<int?> getTotalUsersCount();
  
  /// Get users created after timestamp
  @Query('SELECT * FROM UserLocalModel WHERE createdAt > :timestamp ORDER BY createdAt DESC')
  Future<List<UserLocalModel>> getUsersCreatedAfter(int timestamp);

  /// Get users updated after timestamp
  @Query('SELECT * FROM UserLocalModel WHERE updatedAt > :timestamp ORDER BY updatedAt DESC')
  Future<List<UserLocalModel>> getUsersUpdatedAfter(int timestamp);
  
  // ==================== ADVANCED OPERATIONS ====================
  
  /// Upsert user (insert or update if exists)
  /// Floor không có built-in upsert, nên tạo custom method
  Future<void> upsertUser(UserLocalModel user) async {
    final existingUser = await getUserById(user.id);
    if (existingUser != null) {
      await updateUser(user);
    } else {
      await insertUser(user);
    }
  }
  
  /// Save user with current timestamp
  Future<void> saveUser(UserLocalModel user) async {
    final now = DateTime.now().millisecondsSinceEpoch;
    final userToSave = user.copyWith(
      updatedAt: now,
      createdAt: user.createdAt == 0 ? now : user.createdAt,
    );
    await upsertUser(userToSave);
  }
  
  /// Save user and mark as pending sync (for offline operations)
  Future<void> saveUserOffline(UserLocalModel user) async {
    final now = DateTime.now().millisecondsSinceEpoch;
    final userToSave = user.copyWith(
      updatedAt: now,
      syncStatus: 'pending_sync',
    );
    await upsertUser(userToSave);
  }
}
