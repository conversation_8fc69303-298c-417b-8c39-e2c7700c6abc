import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';
import 'package:sales_app/core/utils/app_logger.dart';
import 'package:sales_app/domain/services/storage_service.dart';

@injectable
class AuthInterceptor extends Interceptor {
  final StorageService _storageService;

  AuthInterceptor(this._storageService);

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) async {
    try {
      final token = await _storageService.getAccessToken();
      if (token != null && token.isNotEmpty) {
        options.headers['Authorization'] = 'Bearer $token';
      }
    } catch (e) {
      // Log error but continue with request
      AppLogger.error('Error adding auth token', error: e);
    }

    handler.next(options);
  }

  // Note: 401 error handling is now done by TokenRefreshInterceptor
  // This interceptor only handles adding auth tokens to requests
}
