import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';
import 'package:sales_app/core/utils/app_logger.dart';

/// Simple retry interceptor with sensible defaults
/// Much simpler than the complex RetryInterceptor with multiple configurations
@injectable
class SimpleRetryInterceptor extends Interceptor {
  final int maxRetries;
  final Duration initialDelay;
  final bool enableLogging;

  SimpleRetryInterceptor({
    this.maxRetries = 3,
    this.initialDelay = const Duration(milliseconds: 1000),
    this.enableLogging = true,
  });

  /// Factory for auth endpoints - more conservative
  factory SimpleRetryInterceptor.auth() {
    return SimpleRetryInterceptor(
      maxRetries: 2,
      initialDelay: const Duration(milliseconds: 1500),
    );
  }

  /// Factory for development - more aggressive
  factory SimpleRetryInterceptor.development() {
    return SimpleRetryInterceptor(
      maxRetries: 5,
      initialDelay: const Duration(milliseconds: 500),
    );
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) async {
    if (!_shouldRetry(err)) {
      _log('Not retrying: ${err.type}');
      handler.next(err);
      return;
    }

    final currentAttempt = _getCurrentAttempt(err.requestOptions);
    
    if (currentAttempt >= maxRetries) {
      _log('Max retries exceeded: $currentAttempt/$maxRetries');
      handler.next(err);
      return;
    }

    final nextAttempt = currentAttempt + 1;
    final delay = _calculateDelay(nextAttempt);

    _log('Retrying attempt $nextAttempt/$maxRetries after ${delay.inMilliseconds}ms');

    await Future.delayed(delay);

    try {
      // Simple retry - create new request
      final retryOptions = err.requestOptions.copyWith(
        extra: {
          ...err.requestOptions.extra,
          'retry_attempt': nextAttempt,
        },
      );

      final dio = Dio();
      dio.options.baseUrl = err.requestOptions.baseUrl;
      dio.options.headers.addAll(err.requestOptions.headers);

      final response = await dio.request(
        retryOptions.path,
        data: retryOptions.data,
        queryParameters: retryOptions.queryParameters,
        options: Options(method: retryOptions.method),
      );

      _log('Retry successful on attempt $nextAttempt');
      handler.resolve(response);
    } catch (retryError) {
      if (retryError is DioException) {
        // Continue retrying
        onError(retryError, handler);
      } else {
        _log('Unexpected error during retry: $retryError');
        handler.next(err);
      }
    }
  }

  /// Simple retry logic - only retry on network/server errors
  bool _shouldRetry(DioException err) {
    if (maxRetries <= 0) return false;
    if (err.type == DioExceptionType.cancel) return false;

    // Retry on network issues
    if (err.type == DioExceptionType.connectionTimeout ||
        err.type == DioExceptionType.sendTimeout ||
        err.type == DioExceptionType.receiveTimeout ||
        err.type == DioExceptionType.connectionError) {
      return true;
    }

    // Retry on server errors (5xx)
    final statusCode = err.response?.statusCode;
    if (statusCode != null && statusCode >= 500) {
      return true;
    }

    return false;
  }

  int _getCurrentAttempt(RequestOptions options) {
    return options.extra['retry_attempt'] as int? ?? 0;
  }

  /// Simple exponential backoff with reasonable limits
  Duration _calculateDelay(int attempt) {
    final delayMs = initialDelay.inMilliseconds * (1 << (attempt - 1));
    // Clamp between initial delay and 30 seconds
    final clampedMs = delayMs.clamp(initialDelay.inMilliseconds, 30000);
    return Duration(milliseconds: clampedMs);
  }

  void _log(String message) {
    if (enableLogging) {
      AppLogger.debug('SimpleRetryInterceptor: $message');
    }
  }
}
