# Network Layer Architecture

> **📚 Comprehensive Guide**: <PERSON>em chi tiết đầy đủ về Mock API System tại [docs/SYSTEMS_GUIDE.md](../../docs/SYSTEMS_GUIDE.md#-mock-api-system)

## Overview

This directory contains the complete network layer implementation following Clean Architecture principles. It provides a flexible foundation for API integration that supports both manual API implementation and future OpenAPI code generation with minimal refactoring.

## Architecture Compliance

✅ **Clean Architecture**: Network logic belongs to Data layer (external concerns)
✅ **Separation of Concerns**: Network configuration separated from business logic
✅ **Dependency Rule**: Core layer doesn't depend on network implementations
✅ **Mock API System**: Complete mock implementation for development

## 📁 Directory Structure

```
lib/data/network/
├── README.md                           # This documentation
├── api_client.dart                     # Manages multiple Dio instances
├── base_api_service.dart              # Base class for manual APIs
├── dio_builder.dart                   # Factory for configured Dio instances
├── open_api_client.dart               # Wrapper for future generated APIs
└── interceptors/
    ├── auth_interceptor.dart          # Authentication interceptor
    ├── error_interceptor.dart         # Error handling interceptor
    ├── logging_interceptor.dart       # Request/response logging
    ├── simple_retry_interceptor.dart  # Simple automatic retry logic
    ├── README_RETRY.md                # Retry system documentation
    └── mock/                          # Mock API interceptors
        ├── README.md                  # Mock system documentation
        ├── base_mock_interceptor.dart # Base class for mock interceptors
        ├── mock_auth_interceptor.dart # Auth mock implementation
        └── test_mock_interceptors.dart # Test utilities
```

## Architecture Components

### 1. DioBuilder
- **Purpose**: Factory for creating configured Dio instances
- **Features**: Supports custom timeouts, interceptors, proxy, headers
- **Usage**: Used by ApiClient to create Dio instances

### 2. ApiClient
- **Purpose**: Manages multiple Dio instances with different configurations
- **Features**:
  - Caches Dio instances for reuse
  - Provides pre-configured instances (default, auth, upload)
  - Supports custom Dio creation
- **Usage**: Injected into API services and OpenApiClient

### 3. BaseApiService
- **Purpose**: Base class for manual API implementations
- **Features**:
  - Common error handling with Either pattern
  - Helper methods for different response types
  - Access to different Dio instances
- **Usage**: Extended by manual API services

### 4. OpenApiClient
- **Purpose**: Wrapper for future generated APIs
- **Features**:
  - Will contain generated API instances
  - Provides access to configured Dio instances during transition
- **Usage**: Will replace manual APIs gradually

### 5. Interceptors
- **AuthInterceptor**: Always adds authentication headers when token is available
- **LoggingInterceptor**: Logs requests and responses (core interceptor)
- **ErrorInterceptor**: Handles common errors (core interceptor)
- **SimpleRetryInterceptor**: Simple automatic retry logic for failed requests
- **Mock Interceptors**: Provides mock API functionality for development

#### Flexible Interceptor Configuration
The system now supports flexible interceptor configurations:
- **Default Dio**: Includes all interceptors (logging, retry, mock, auth, error)
- **Auth Dio**: Conservative retry + excludes AuthInterceptor for authentication endpoints
- **Public Dio**: Standard retry for public endpoints
- **Custom Dio**: Allows specific interceptor combinations
- **Core Interceptors**: Logging + Retry + Error (recommended minimum)

#### Interceptor Order
The interceptors are applied in the following order for optimal functionality:
1. **LoggingInterceptor** - Request/response logging
2. **SimpleRetryInterceptor** - Simple automatic retry logic
3. **MockInterceptor** - Mock API responses (development only)
4. **AuthInterceptor** - Authentication headers
5. **ErrorInterceptor** - Error handling and transformation

## 🚀 Usage Examples

### Current Implementation (Manual APIs)

```dart
// 1. Create API service (Auth service excludes AuthInterceptor)
@injectable
class AuthApiService extends BaseApiService {
  AuthApiService(super.apiClient)
    : super(customDio: apiClient.createDioWithoutAuth(config: DioConfig.authConfig));

  Future<Either<Failure, LoginResponse>> login(LoginRequest request) async {
    return handleApiCall(
      () => dio.post(ApiPaths.authLogin, data: request.toJson()),
      (data) => LoginResponse.fromJson(data),
    );
  }
}

// 2. Use in repository
@injectable
class AuthRepositoryImpl implements AuthRepository {
  final AuthApiService _authApiService;

  AuthRepositoryImpl(this._authApiService);

  Future<Either<Failure, User>> login(String email, String password) async {
    final request = LoginRequest(email: email, password: password);
    final result = await _authApiService.login(request);
    return result.map((response) => response.user);
  }
}
```

### Future Implementation (Generated APIs)

```dart
// Generated API will be used through OpenApiClient
@injectable
class AuthRepositoryImpl implements AuthRepository {
  final OpenApiClient _openApiClient;

  AuthRepositoryImpl(this._openApiClient);

  Future<Either<Failure, User>> login(String email, String password) async {
    try {
      // Use generated API
      final response = await _openApiClient.authApi.login(
        LoginRequest(email: email, password: password)
      );
      return Right(response.data!.user);
    } catch (e) {
      return Left(_handleError(e));
    }
  }
}
```

## Configuration Options

### Different Dio Instances

```dart
// Default API calls
final response = await apiClient.defaultDio.get('/users');

// Authentication APIs (longer timeout)
final authResponse = await apiClient.authDio.post(ApiPaths.authLogin);

// File uploads (very long timeout)
final uploadResponse = await apiClient.uploadDio.post('/upload');

// Custom configuration
final customDio = apiClient.createCustomDio(
  baseUrl: 'https://different-api.com',
  sendTimeout: 120,
  headers: {'Custom-Header': 'value'},
);
```

### Custom Interceptors

```dart
final customDio = apiClient.createCustomDio(
  baseUrl: 'https://api.example.com',
  customInterceptors: [
    CustomInterceptor(),
    AnotherInterceptor(),
  ],
);
```

## Mock API System

The network layer includes a comprehensive mock API system for development:

- **Mock Mode**: Set `AppConstants.isMockMode = true`
- **Auth APIs**: Login, logout, register, refresh token
- **Mock Users**: Pre-defined test accounts
- **Network Simulation**: Realistic delays and error simulation

See `interceptors/mock/README.md` for detailed mock API documentation.

## Retry System

The network layer includes a simple automatic retry system for handling transient network errors:

- **Simple Exponential Backoff**: Proven retry strategy with reasonable limits
- **Environment-aware**: More aggressive in development, conservative in production
- **API-specific Policies**: Conservative retry for auth, standard for public APIs
- **OpenAPI Compatible**: Works seamlessly with future generated clients
- **Easy Configuration**: Sensible defaults with simple customization

### Retry Configuration by API Type

- **Default API**: Environment-based retry (dev: 5 retries, prod: 3 retries)
- **Auth API**: Conservative retry (2 retries, longer delay)
- **Public API**: Standard retry (3 retries, exponential backoff)
- **Upload API**: No retry (large files can be time-consuming)

See `interceptors/README_RETRY.md` for detailed retry system documentation.

## Error Handling

The architecture uses the Either pattern for error handling:

```dart
final result = await authRepository.login(email, password);

result.fold(
  (failure) {
    // Handle error
    if (failure is NetworkFailure) {
      showNetworkError();
    } else if (failure is AuthFailure) {
      showAuthError();
    }
  },
  (user) {
    // Handle success
    navigateToHome(user);
  },
);
```

## Migration Strategy

### Phase 1: Current Setup ✅
1. Implement manual APIs using BaseApiService
2. Use ApiClient for Dio configuration
3. Build repository layer on top of manual APIs
4. Mock API system for development

### Phase 2: OpenAPI Integration (Future)
1. Generate APIs using openapi_generator
2. Update OpenApiClient to use generated APIs
3. Gradually replace manual API calls with generated ones
4. Keep repository layer interface unchanged

### Phase 3: Complete Migration (Future)
1. Remove unused manual APIs
2. Clean up BaseApiService if no longer needed
3. Optimize OpenApiClient

## Benefits

1. **Clean Architecture Compliance**: Network logic in appropriate layer
2. **Flexible Dio Configuration**: Multiple instances for different needs
3. **Zero Refactoring**: Repository layer interface remains unchanged
4. **Gradual Migration**: Can migrate APIs one by one
5. **Error Handling**: Consistent error handling across all APIs
6. **Automatic Retry**: Intelligent retry logic for transient failures
7. **Mock System**: Complete mock API for development
8. **Testability**: Easy to mock and test
9. **Future-Proof**: Ready for OpenAPI code generation
10. **Resilient Network**: Handles network instability gracefully

## OpenAPI Generator Integration (Future)

When ready to use openapi_generator:

```bash
java -jar openapi-generator-cli.jar generate \
  -i swagger.yaml \
  -g dart-dio \
  -o lib/api_collection \
  --additional-properties=serializationLibrary=json_serializable
```

The generated APIs will work seamlessly with the existing ApiClient configuration.
