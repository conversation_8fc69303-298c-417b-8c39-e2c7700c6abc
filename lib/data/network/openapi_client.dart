import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';
import 'package:sales_app/data/network/api_client.dart';

/// OpenAPI Client wrapper for generated APIs
/// This will be used when we have generated APIs from openapi_generator
@singleton
class OpenApiClient {
  final ApiClient _defaultApiClient;
  final ApiClient _authApiClient;
  final ApiClient _uploadApiClient;

  // These will be uncommented when we have generated APIs
  // late final Openapi _openapi;
  // late final AuthApi _authApi;
  // late final UserApi _userApi;
  // late final ProductApi _productApi;

  OpenApiClient(
    @Named('default') this._defaultApiClient,
    @Named('auth') this._authApiClient,
    @Named('upload') this._uploadApiClient,
  ) {
    // Initialize generated API client when available
    // _openapi = Openapi(dio: _defaultApiClient.dio);
    // _authApi = _openapi.getAuthApi();
    // _userApi = _openapi.getUserApi();
    // _productApi = _openapi.getProductApi();
  }

  /// Get configured Dio instances for manual API creation
  /// This is useful during the transition period
  Dio get dio => _defaultApiClient.dio;
  Dio get authDio => _authApiClient.dio;
  Dio get uploadDio => _uploadApiClient.dio;

  // These getters will be uncommented when we have generated APIs
  // AuthApi get authApi => _authApi;
  // UserApi get userApi => _userApi;
  // ProductApi get productApi => _productApi;

  /// Create a custom Dio instance for specific needs
  Dio createCustomDio({
    required String baseUrl,
    List<Interceptor>? interceptors,
    String? proxy,
    int? sendTimeout,
    int? connectTimeout,
    int? receiveTimeout,
    Map<String, dynamic>? headers,
  }) {
    return ApiClient(
      interceptors: interceptors ?? [],
      config: DioConfig(
        baseUrl: baseUrl,
        proxy: proxy,
        sendTimeout: sendTimeout ?? 30,
        connectTimeout: connectTimeout ?? 30,
        receiveTimeout: receiveTimeout ?? 30,
        headers: headers,
      ),
    ).dio;
  }
}

/// Future implementation when we have generated APIs:
///
/// @singleton
/// class OpenApiClient {
///   final ApiClient _apiClient;
///   late final Openapi _openapi;
///
///   OpenApiClient(this._apiClient) {
///     _openapi = Openapi(dio: _apiClient.defaultDio);
///   }
///
///   AuthApi get authApi => _openapi.getAuthApi();
///   UserApi get userApi => _openapi.getUserApi();
///   ProductApi get productApi => _openapi.getProductApi();
/// }
