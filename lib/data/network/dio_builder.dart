import 'package:dio/dio.dart';

class DioBuilder {
  const DioBuilder._();

  static Dio createDio(
    String baseUrl, {
    List<Interceptor> interceptors = const [],
    String? proxy,
    int sendTimeout = 10,
    int connectTimeout = 10,
    int receiveTimeout = 10,
    Map<String, dynamic>? headers,
  }) {
    BaseOptions options = BaseOptions(
      baseUrl: baseUrl,
      sendTimeout: Duration(seconds: sendTimeout),
      connectTimeout: Duration(seconds: connectTimeout),
      receiveTimeout: Duration(seconds: receiveTimeout),
      headers: headers ?? {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    );

    final dio = Dio(options);

    // Add proxy if provided
    if (proxy != null) {
      // Note: Proxy configuration would need platform-specific implementation
      // For now, we'll skip proxy configuration
      // TODO: Implement proxy configuration when needed
    }

    // Add interceptors
    if (interceptors.isNotEmpty) {
      dio.interceptors.addAll(interceptors);
    }

    return dio;
  }
}
