class User {
  final String id;
  final String email;
  final String name;
  final String? avatar;
  final String? phone;

  const User({
    required this.id,
    required this.email,
    required this.name,
    this.avatar,
    this.phone,
  });

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is User &&
        other.id == id &&
        other.email == email &&
        other.name == name &&
        other.avatar == avatar &&
        other.phone == phone;
  }

  @override
  int get hashCode {
    return Object.hash(
      id,
      email,
      name,
      avatar,
      phone,
    );
  }

  @override
  String toString() {
    return 'User(id: $id, email: $email, name: $name, avatar: $avatar, phone: $phone)';
  }
} 