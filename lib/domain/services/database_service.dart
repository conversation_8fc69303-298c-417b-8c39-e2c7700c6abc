import '../entities/user.dart';

/// Abstract Database Service
/// Đ<PERSON><PERSON> nghĩa interface cho local database operations
/// Theo Clean Architecture, domain layer chỉ biết về abstract interface
abstract class DatabaseService {
  
  // ==================== USER OPERATIONS ====================
  
  /// Save user to local database
  Future<void> saveUser(User user);
  
  /// Get user by ID from local database
  Future<User?> getUserById(String id);
  
  /// Get user by email (for login)
  Future<User?> getUserByEmail(String email);
  
  /// Get all users from local database
  Future<List<User>> getAllUsers();
  
  /// Delete user by ID
  Future<void> deleteUser(String id);
  
  /// Check if user exists in local database
  Future<bool> userExists(String id);
  
  // ==================== OFFLINE OPERATIONS ====================
  
  /// Save user for offline sync (mark as pending)
  Future<void> saveUserOffline(User user);
  
  /// Get users that need to be synced to server
  Future<List<User>> getPendingSyncUsers();
  
  /// Get users with failed sync attempts
  Future<List<User>> getFailedSyncUsers();
  
  /// Mark user as successfully synced
  Future<void> markUserAsSynced(String userId, {int? serverTimestamp});
  
  /// Mark user sync as failed
  Future<void> markUserSyncFailed(String userId);
  
  // ==================== UTILITY OPERATIONS ====================
  
  /// Clear all user data (for logout)
  Future<void> clearAllUsers();
  
  /// Get total users count
  Future<int> getUsersCount();
  
  /// Get database info for debugging
  Future<Map<String, dynamic>> getDatabaseInfo();
  
  /// Close database connection
  Future<void> closeDatabase();
  
  // ==================== FUTURE EXTENSIONS ====================
  // Khi thêm các entities khác (Customer, Product, etc.)
  // sẽ thêm methods tương ứng ở đây
  
  // Customer operations (future)
  // Future<void> saveCustomer(Customer customer);
  // Future<Customer?> getCustomerById(String id);
  // Future<List<Customer>> getAllCustomers();
  
  // Product operations (future)
  // Future<void> saveProduct(Product product);
  // Future<Product?> getProductById(String id);
  // Future<List<Product>> getAllProducts();
  
  // Transaction operations (future)
  // Future<void> saveTransaction(Transaction transaction);
  // Future<List<Transaction>> getTransactionsByUserId(String userId);
}
