import 'dart:async';
import 'package:sales_app/domain/entities/network_connection_status.dart';

/// Domain service for network monitoring business logic
/// Handles stability tracking, thresholds, and connection analysis
/// Follows clean architecture by keeping business logic in domain layer
abstract class NetworkMonitoringService {
  /// Stream of network connection status changes
  Stream<NetworkConnectionStatus> get connectionStatusStream;

  /// Current network connection status
  NetworkConnectionStatus get currentStatus;
  
  /// Initialize network monitoring
  Future<void> initialize();
  
  /// Manually check connection status
  Future<void> checkConnection();
  
  /// Force connected state (for emergency bypass)
  void forceConnected();
  
  /// Handle connectivity change from network info
  void handleConnectivityChange(bool isConnected);
  
  /// Dispose resources
  void dispose();
}

/// Configuration for network monitoring behavior
class NetworkMonitoringConfig {
  /// Number of disconnections to consider connection unstable
  final int unstableThreshold;
  
  /// Time window for counting disconnections
  final Duration unstableWindow;
  
  /// Delay before checking connection (for UX)
  final Duration checkDelay;
  
  /// Duration to wait before clearing unstable state
  final Duration stabilizationDuration;
  
  const NetworkMonitoringConfig({
    this.unstableThreshold = 3,
    this.unstableWindow = const Duration(minutes: 2),
    this.checkDelay = const Duration(seconds: 2),
    this.stabilizationDuration = const Duration(minutes: 5),
  });
  
  /// Default configuration
  static const NetworkMonitoringConfig defaultConfig = NetworkMonitoringConfig();
  
  /// Development configuration (more sensitive)
  static const NetworkMonitoringConfig developmentConfig = NetworkMonitoringConfig(
    unstableThreshold: 2,
    unstableWindow: Duration(minutes: 1),
    checkDelay: Duration(seconds: 1),
    stabilizationDuration: Duration(minutes: 2),
  );
}
