/// Interface cho Logger Service
/// <PERSON><PERSON><PERSON> nghĩa các phương thức logging cần thiết cho ứng dụng
abstract class LoggerService {
  /// Log thông tin chung
  void logInfo(String message, {Map<String, dynamic>? data});

  /// Log cho quá trình debug
  void logDebug(String message, {Map<String, dynamic>? data});

  /// Log cảnh báo
  void logWarning(String message, {Map<String, dynamic>? data});

  /// Log lỗi nghiêm trọng
  void logError(
    String message, {
    Object? error,
    StackTrace? stackTrace,
    Map<String, dynamic>? data,
  });

  /// Log sự kiện (user action, API call, etc.)
  void logEvent(String event, {Map<String, dynamic>? data});

  /// Log network request
  void logNetworkRequest(
    String method,
    String url, {
    Map<String, dynamic>? headers,
    Map<String, dynamic>? body,
  });

  /// Log network response
  void logNetworkResponse(
    String method,
    String url,
    int statusCode, {
    Map<String, dynamic>? response,
    Duration? duration,
  });
} 