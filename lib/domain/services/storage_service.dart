import '../entities/user.dart';

abstract class StorageService {
  // Auth token methods
  Future<void> saveAccessToken(String token);
  Future<String?> getAccessToken();

  Future<void> saveRefreshToken(String token);
  Future<String?> getRefreshToken();

  // Token expiry tracking
  Future<void> saveTokenExpiryTime(DateTime expiryTime);
  Future<DateTime?> getTokenExpiryTime();
  Future<bool> isTokenExpired();

  // User info methods
  Future<void> saveUserInfo(User user);
  Future<User?> getUserInfo();

  // Environment configuration methods
  Future<void> saveSelectedEnvironment(String environmentKey);
  Future<String?> getSelectedEnvironment();

  // Clear methods
  Future<void> clearAll();
  Future<void> clearTokens();
}