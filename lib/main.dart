import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:sales_app/di/injection.dart';
import 'package:sales_app/core/theme/app_theme.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:sales_app/generated/l10n.dart';
import 'package:sales_app/domain/services/environment_service.dart';

// Router imports (Clean Architecture compliant)
import 'presentation/router/app_router.dart';

// Network monitoring imports
import 'package:sales_app/presentation/widgets/network/network.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await configureDependencies();

  // Initialize EnvironmentService to load saved environment
  final environmentService = getIt<EnvironmentService>();
  await environmentService.initialize();

  runApp(
    const ProviderScope(
      child: MyApp(),
    ),
  );
}

class MyApp extends ConsumerWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Get router instance from provider
    final router = ref.watch(routerProvider);

    // Design size cho iPhone 12 Pro
    return ScreenUtilInit(
      designSize: const Size(390, 844),
      minTextAdapt: true,
      splitScreenMode: true,
      builder: (context, child) {
        return MaterialApp.router(
          title: 'KienlongBank Sales App',
          debugShowCheckedModeBanner: false,
          theme: AppTheme.lightTheme,
          routerConfig: router,
          builder: NetworkAwareAppBuilder.build,
          localizationsDelegates: const [
            S.delegate,
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          supportedLocales: S.delegate.supportedLocales,
        );
      },
    );
  }
}