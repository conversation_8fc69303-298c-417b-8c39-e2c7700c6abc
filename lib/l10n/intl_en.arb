{"@@locale": "en", "appName": "KienlongBank Sales App", "home": "Home", "password": "Password", "login": "<PERSON><PERSON>", "introduction": "Introduction", "register": "Register", "register_account": "Register account", "documentVerificationGuide": "Document Verification Guide", "notesWhenTakingDocuments": "Notes when taking documents:", "avoidUsingImages": "Avoid using images like:", "checkFrontSide": "Check front side of ID", "checkBackSide": "Check back side of ID", "pleaseCheckPhoto": "Please check the photo", "placeDocumentInFrame": "Please place the document in the frame for verification", "clickToCapture": "<PERSON>lick to capture", "personalInfoConfirmation": "Personal Information Confirmation", "infoFromId": "Information from ID", "additionalInfo": "Additional Information", "phoneNumber": "Phone Number", "enterPhoneNumber": "Enter phone number", "position": "Position", "province": "Province/City", "selectProvince": "Select province/city", "branch": "Branch", "selectBranch": "Select branch", "branchAddress": "Branch Address", "referralCode": "Referral Code (if any)", "enterReferralCode": "Enter referral code", "registrationSuccess": "Registration Successful", "bankWillContact": "KienlongBank will contact you soon to inform the result!", "close": "Close", "fullName": "Full Name", "idNumber": "ID Number", "residentialAddress": "Residential Address", "newCTV": "New CTV", "dailyInstallmentLoan": "Daily Installment Loan", "benefitsAsCTV": "Benefits as CTV", "stepsToBecomeCTV": "Steps to become a CTV", "dailyInstallmentLoanDesc": "Daily Installment Loan is a loan product in the form of daily installments (principal, interest) to serve living needs and business activities, other activities.", "suitableFor": "Suitable for individuals with sudden, short-term spending needs; small businesses and individual businesses; customers with stable income and good credit history.", "flexibleWorkingHours": "Flexible working hours", "monthlyServiceFee": "Monthly service fee", "unlimitedIncome": "Unlimited income", "workUniform": "Work uniform provided", "healthInsurance": "Health insurance and accident insurance provided by Kienlongbank when meeting outstanding balance conditions", "holidayBonus": "Holiday bonus for 30/04, National Day 02/9, ...", "requirements": "Requirements:", "vietnameseCitizen": "Vietnamese citizen with legal capacity, civil act capacity and civil liability according to law", "goodBackground": "Good background and character; No criminal record", "ageRequirement": "Age from 18 to 60, graduated from secondary school or higher", "healthRequirement": "Good health, clear mind", "assetRequirement": "Minimum collateral of 150 million VND and commitment to comply with Kienlongbank's minimum average outstanding balance regulations", "areaKnowledge": "Understanding of working area", "noOtherBank": "Not working for other credit institutions or finance companies", "continueText": "Continue", "retake": "Retake", "confirm": "Confirm", "idCard": "ID Card", "passport": "Passport", "errorServerInternal": "Server error. Please try again later.", "errorServerNotFound": "The requested resource was not found.", "errorServerTooManyRequests": "Too many requests. Please try again later.", "errorServerBadRequest": "Invalid request data.", "errorServerUnknown": "An error occurred. Please try again.", "errorNetworkConnectionTimeout": "Connection timeout. Please check your internet connection.", "errorNetworkSendTimeout": "Request timeout. Please try again.", "errorNetworkReceiveTimeout": "Response timeout. Please try again.", "errorNetworkNoConnection": "No internet connection. Please check your network settings.", "errorNetworkRequestCancelled": "Request was cancelled.", "errorNetworkCertificate": "Certificate verification failed.", "errorNetworkUnknown": "Network error. Please try again.", "errorCacheRead": "Failed to read cached data.", "errorCacheWrite": "Failed to save data to cache.", "errorCacheDataCorrupted": "Cached data is corrupted.", "errorCacheStorageFull": "Storage is full.", "errorCacheUnknown": "Cache error occurred.", "errorAuthInvalidCredentials": "Authentication failed. Please login again.", "errorAuthAccessDenied": "Access denied. You don't have permission to perform this action.", "errorAuthTokenExpired": "Session expired. Please login again.", "errorAuthUserNotFound": "User not found.", "errorAuthAccountLocked": "Account is locked or disabled.", "errorAuthUnknown": "Authentication error occurred.", "errorValidationInvalidEmail": "Please enter a valid email address.", "errorValidationInvalidPassword": "Please enter a valid password.", "errorValidationRequiredField": "This field is required.", "errorValidationTooShort": "Input is too short.", "errorValidationTooLong": "Input is too long.", "errorValidationInvalidFormat": "Invalid format.", "errorValidationServer": "Validation failed.", "errorValidationUnknown": "Validation error occurred."}