---
description: 
globs: *.dart
alwaysApply: false
---
# Flutter UI Development Rules for This Project

## 🎨 Theming & Colors

- Always use colors from `AppColor` and themes from `AppTheme` defined in `core/theme/app_theme.dart`.  
  *(<PERSON>ôn sử dụng màu và theme từ AppColor, AppTheme trong core/theme/app_theme.dart.)*
- Do not hardcode colors or themes directly in UI widgets.  
  *(Không hardcode màu hay theme trong widget UI.)*

## 📏 Dimensions (Spacing, Size, Padding)

- Always use dimension constants from `AppDimens` in `core/constants/app_dimens.dart`.  
  *(Luôn dùng các hằng số kích thước trong AppDimens.)*
- If a required dimension does not exist, add it to `AppDimens` instead of hardcoding values like `8.0`, `16.0`, etc.  
  *(Nếu chưa có kích thước cần dùng, h<PERSON>y thêm vào AppDimens, không hardcode.)*

## 🧱 Reusable Widgets

- Always check for existing widgets in `presentation/widgets` before creating
*(<PERSON>r<PERSON><PERSON><PERSON> khi tạo widget mới, kiểm tra widget hiện có trong presentation/widgets.)*
- Reuse or update an existing widget if it satisfies the UI requirement.  
  *(Tái sử dụng hoặc cập nhật widget hiện có nếu phù hợp.)*
- Only create a new widget when existing ones cannot be reused or extended meaningfully.  
  *(Chỉ tạo widget mới khi không thể tái sử dụng widget hiện có.)*

## 📱 UI Design Principles

- UI must be adaptive and work well on both Android and iOS platforms.  
  *(Thiết kế UI đáp ứng, hoạt động tốt trên Android và iOS.)*
- Follow Material Design guidelines for UI components and behaviors.  
  *(Tuân theo chuẩn Material Design cho component và hành vi.)*
- Design new screens to be visually and structurally consistent with existing screens.  
  *(Màn hình mới phải đồng bộ về mặt hình thức và cấu trúc với màn hình cũ.)*
- Always aim to create UIs that are:
  - Visually appealing  
  - Modern  
  - Customizable  
  - Developer-friendly  
  - Consistent with the app's design language  
  *(Luôn hướng tới UI đẹp mắt, hiện đại, dễ tùy biến, thân thiện với dev và đồng bộ với ngôn ngữ thiết kế của app.)*

## 🧾 Naming Conventions

- Screen names must follow a consistent pattern using either `_screen` or `_page`. Do **not** mix both styles.  
  *(Tên màn hình phải theo pattern thống nhất, dùng `_screen` hoặc `_page`, không trộn lẫn.)*
- Prefer using `_screen` as the standard suffix unless otherwise discussed with the team.  
  *(Ưu tiên dùng `_screen` làm chuẩn.)*

## 🧼 Code Maintenance & Safety

- Avoid modifying existing pages or widgets unless absolutely necessary.  
  *(Tránh sửa các trang hoặc widget hiện có nếu không thực sự cần.)*
- Only rename files/widgets when there's a strong reason (e.g., architectural restructure or naming conflict).  
  *(Chỉ rename khi có lý do chính đáng.)*
- If a widget is no longer used after editing UI, **remove it** proactively.  
  *(Widget không dùng nữa thì chủ động loại bỏ.)*
- When modifying core files like `AppColor`, `AppTheme`, or `AppDimens`, ensure backward compatibility.  
  *(Khi sửa core như AppColor, AppTheme, AppDimens phải đảm bảo tương thích ngược.)*
  - If the changes affect existing usages, **identify and fix** all affected areas accordingly.  
  *(Nếu ảnh hưởng đến các phần đã dùng thì phải tìm và sửa lại.)*

## 💡 Design Suggestions by AI

- Always suggest beautiful, modern, and professional UIs.  
  *(Luôn đề xuất UI đẹp, hiện đại, chuyên nghiệp.)*
- Propose solutions that are easy to maintain and reuse.  
  *(Đưa ra giải pháp dễ bảo trì và tái sử dụng.)*
- Respect all the rules above when generating new UI code.  
  *(Tuân thủ tất cả các quy tắc trên khi tạo UI mới.)*

---

## 💅 Code Style & Format Guidelines

- Use `const` whenever possible to optimize rebuilds and performance.  
  *(Dùng `const` ở mọi nơi có thể để tối ưu hiệu năng và giảm rebuild.)*
- Use trailing commas in multi-line widget declarations to support clean formatting via `dart format`.  
  *(Dùng dấu phẩy cuối trong widget nhiều dòng để định dạng code tốt hơn.)*
- Avoid deeply nested widgets; if nesting exceeds 3 levels, extract to separate widget classes.  
  *(Tránh nesting widget quá sâu, >3 cấp thì tách widget riêng.)*
- Use `@override` annotation explicitly when overriding methods.  
  *(Thêm `@override` rõ ràng khi override.)*
- Use `=>` syntax for short single-expression functions.  
  *(Dùng `=>` cho hàm ngắn.)*
- Prefer `final` instead of `var` if the variable is not reassigned.  
  *(Dùng `final` thay cho `var` nếu biến không được gán lại.)*

---

## 🪵 Logging Rules (Sử dụng Logger)

- Always use the configured `logger` with `AppLogger` in `lib/core/app_logger.dart` instead of `print` for logging.  
  *(Luôn dùng `logger` đã cấu hình thay vì `print` để log thông tin.)*

- Use appropriate log levels
  *(Sử dụng đúng cấp độ log tương ứng với nội dung cần ghi log.)*

- Add logging to help with:
  - Debugging user actions or app flows  
  - Capturing errors or unexpected behavior  
  - Monitoring important lifecycle events (e.g., init, dispose, API calls)  
  *(Thêm log để dễ debug, bắt lỗi, theo dõi hành vi quan trọng của app.)*

- Remove excessive logs or sensitive data from release builds.  
  *(Xoá log thừa hoặc nhạy cảm khỏi bản release.)*

---

## 📱 Responsive & Adaptive UI Guidelines

- Never hardcode sizes (width, height). Use `MediaQuery`, `LayoutBuilder`, or responsive libraries like:  
  - `flutter_screenutil`  
  *(Không hardcode kích thước. Dùng MediaQuery, LayoutBuilder hoặc thư viện.)*
- Use adaptive layout widgets such as `Flexible`, `Expanded`, `Spacer`, `SafeArea`, `FittedBox`, `AspectRatio`.  
  *(Sử dụng widget hỗ trợ responsive như Flexible, Expanded, SafeArea, v.v.)*
- Scale font sizes, padding, and spacing based on screen size (e.g. `14.sp`, `16.h` with screenutil).  
  *(Scale font size, padding theo kích thước màn hình.)*
- Test UI on multiple device types and screen sizes to ensure consistent appearance.  
  *(Kiểm thử UI trên nhiều loại thiết bị, kích thước khác nhau.)*
- Prevent layout overflow issues, especially when the keyboard is visible or with long content.  
  *(Tránh tràn layout khi bàn phím bật hoặc nội dung dài.)*

---

## 🧭 Routing Rules (Sử dụng `go_router`)

- All navigation must use the configured `go_router` setup.  
  *(Tất cả điều hướng phải sử dụng theo cấu hình của `go_router`.)*

- Never use `Navigator.push` or `Navigator.of(context)` directly.  
  *(Không được sử dụng trực tiếp `Navigator.push` hoặc `Navigator.of(context)`.)*

- Use `context.go()` or `context.push()` for navigation:  
  - `context.go('/path')` for **replacing** the current route  
  - `context.push('/path')` for **stacking** a new route  
  *(Sử dụng đúng phương thức tuỳ trường hợp: `go` để thay thế, `push` để thêm route.)*

- Define routes and path constants in a central place, e.g., `core/router/app_routes.dart`.  
  *(Khai báo tất cả route và path ở một nơi duy nhất như `core/router/app_routes.dart`.)*

- Use strongly typed route parameters when possible (e.g., `GoRouteData` or named parameters).  
  *(Ưu tiên sử dụng tham số có kiểu rõ ràng để điều hướng.)*

- Use `ShellRoute` or nested routes for tab/navigation structures.  
  *(Dùng `ShellRoute` hoặc nested routes cho các layout/tab phức tạp.)*

- Avoid defining route paths inline or hardcoding strings.  
  *(Không khai báo path trực tiếp hoặc hardcode trong code.)*

---

## ✅ Usage Notes

- This file can be placed as `AI_INSTRUCTIONS.md` at the root of your project.  
- Can be copy-pasted into **Cursor → Custom Instructions** for AI-assisted UI/code generation.  
- Review and update quarterly or whenever your design system evolves.  

---

