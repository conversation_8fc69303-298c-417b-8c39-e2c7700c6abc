---
description: 
globs: *.dart
alwaysApply: false
---
RULE: Flutter Localization Automation

OBJECTIVE:
1. Tự động thêm chuỗi text vào file `intl_en.arb` và `intl_vi.arb` (nếu chưa tồn tại).
2. Tự động chạy `flutter pub run intl_utils:generate` để generate code localization.
3. Thay thế toàn bộ các đoạn text hardcoded (string fix cứng) trong mã nguồn Dart bằng `S.of(context).<key>`.
4. Tối ưu và sửa các lỗi thường gặp (cú pháp sai, thiếu import, key trùng, format JSON, lỗi l10n...).

INSTRUCTIONS:

- Khi phát hiện chuỗi text cứng như: `Text("Đăng nhập")`, `Text("Login")`, `"Mật khẩu"`... → tự động:
  → Gán key chuẩn hóa (snake_case, không dấu, không khoảng trắng).  
     → Ví dụ: `"Đăng nhập"` → `login`, `"Mật khẩu"` → `password`.

- Tìm và kiểm tra key trong `intl_vi.arb` và `intl_en.arb`:
  → Nếu chưa có: thêm vào cả hai file.
  → Gợi ý tiếng Anh nếu chưa có bản dịch (có thể để `"TODO"` nếu chưa rõ).

- Format chuẩn `.arb`:
  ```json
  "login": "Đăng nhập",
  "@login": {
    "description": "Nút đăng nhập"
  }
- Luôn thiết kế, tổ chức cấu trúc thư mục tuân thủ clean architecture.
- Luôn review các file, class đã tạo trước đó để đảm bảo tái sử dụng lại, nếu cần có thể update.
- Luôn sử dụng getIt và Injectable cho DI, không sử dụng riverpod cho DI.
- Trong quá trình run app sẽ có một vài file được gen tự động, kiểm tra những file đó nếu cần có thể thêm vào .gitignore.
- Luôn cố viết code như một senior developers.
- Luôn định nghĩa các enum, data, constants,... nếu cần thiết.
