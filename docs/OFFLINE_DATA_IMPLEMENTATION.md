# 📱 Offline Data Implementation Guide

## 🎯 Overview

Sales App hiện đã được implement với **Offline Data** và **Offline Support** sử dụng Floor ORM. Hệ thống cho phép app hoạt động khi không có mạng và tự động sync khi có kết nối trở lại.

## 🏗️ Architecture

### **Database Layer**
```
lib/data/datasources/local/
├── dao/
│   └── user_dao.dart             # Data Access Object
├── database/
│   ├── app_database.dart         # Main Database class
│   └── app_database.g.dart       # Generated Floor code
├── user_local_datasource.dart    # Local data source interface
└── user_local_datasource_impl.dart # Local data source implementation

lib/data/models/local/
└── user_local_model.dart         # Floor Entity cho User table
```

### **Service Layer**
```
lib/domain/services/
└── database_service.dart         # Abstract Database Service

lib/data/services_impl/
└── database_service_impl.dart    # Floor implementation
```

## 📊 Database Schema

### **Users Table**
| Column | Type | Description |
|--------|------|-------------|
| `id` | TEXT PRIMARY KEY | User ID |
| `email` | TEXT | User email |
| `name` | TEXT | User name |
| `avatar` | TEXT | Avatar URL (nullable) |
| `phone` | TEXT | Phone number (nullable) |
| `createdAt` | INTEGER | Local creation timestamp |
| `updatedAt` | INTEGER | Local update timestamp |
| `syncStatus` | TEXT | Sync status: 'synced', 'pending_sync', 'sync_failed' |
| `serverUpdatedAt` | INTEGER | Server timestamp (nullable) |

## 🔄 Offline-First Login Flow

### **Online Login**
```
1. User enters credentials
2. Check network connectivity
3. Call API login
4. Store tokens in secure storage
5. Save user to local database
6. Return success
```

### **Offline Login**
```
1. User enters credentials
2. No network detected
3. Check local database for user by email
4. If user exists locally → allow login
5. Return success with local user data
```

### **Hybrid Login (API Fails)**
```
1. User enters credentials
2. Network available but API fails
3. Fallback to offline login
4. Use cached user data
```

## 🛠️ Key Features

### **✅ Implemented**

#### **Database Operations**
- ✅ Save user to local database
- ✅ Get user by ID/email
- ✅ Get all users
- ✅ Delete user
- ✅ Clear all users (logout)

#### **Offline Support**
- ✅ Offline login with cached credentials
- ✅ Offline-first authentication
- ✅ Graceful fallback when API fails
- ✅ Network connectivity detection

#### **Sync Management**
- ✅ Track sync status (synced/pending/failed)
- ✅ Mark users as pending sync
- ✅ Mark users as synced with server timestamp

### **🔄 Future Extensions**

#### **Additional Tables**
```dart
// Customer table (future)
@Entity(tableName: 'customers')
class CustomerEntity {
  @primaryKey
  final String id;
  final String name;
  final String email;
  final String phone;
  final String syncStatus;
  // ... other fields
}

// Product table (future)
@Entity(tableName: 'products')
class ProductEntity {
  @primaryKey
  final String id;
  final String name;
  final String description;
  final double price;
  // ... other fields
}
```

#### **Advanced Sync Features**
- Background sync service
- Conflict resolution
- Batch sync operations
- Incremental sync

## 📱 Usage Examples

### **Login with Offline Support**
```dart
// In AuthRepository
final result = await authRepository.login(
  email: '<EMAIL>',
  password: 'password123',
);

result.fold(
  (failure) => print('Login failed: ${failure.message}'),
  (user) => print('Login successful: ${user.name}'),
);
```

### **Check Login Status**
```dart
final isLoggedIn = await authRepository.isLoggedIn();
// Returns true if user has valid token OR exists in local database
```

### **Database Operations**
```dart
// Save user offline
await databaseService.saveUserOffline(user);

// Get pending sync users
final pendingUsers = await databaseService.getPendingSyncUsers();

// Mark as synced
await databaseService.markUserAsSynced(userId, serverTimestamp: timestamp);
```

## 🔧 Configuration

### **Dependencies Added**
```yaml
dependencies:
  floor: ^1.4.2
  sqflite: ^2.3.0
  path: ^1.8.3

dev_dependencies:
  floor_generator: ^1.4.2
```

### **Build Commands**
```bash
# Generate Floor code
flutter packages pub run build_runner build --delete-conflicting-outputs

# Clean and regenerate
flutter packages pub run build_runner clean
flutter packages pub run build_runner build --delete-conflicting-outputs
```

## 🚨 Important Notes

### **Security Considerations**
- ⚠️ **Password Storage**: Hiện tại chưa store password hash cho offline verification
- ⚠️ **Data Encryption**: Local database chưa được encrypt
- ⚠️ **Token Validation**: Offline login không validate token expiry

### **Performance Considerations**
- ✅ **Database Size**: Chỉ store essential data
- ✅ **Query Optimization**: Sử dụng indexes cho frequent queries
- ✅ **Memory Usage**: Close database connections properly

### **Error Handling**
- ✅ **Network Failures**: Graceful fallback to offline mode
- ✅ **Database Errors**: Proper error logging and recovery
- ✅ **Sync Conflicts**: Track failed sync attempts

## 🎯 Next Steps

### **Phase 1: Current (Completed)**
- ✅ Basic Floor setup
- ✅ User table implementation
- ✅ Offline login support

### **Phase 2: Enhancement**
- 🔲 Customer table implementation
- 🔲 Product catalog caching
- 🔲 Document storage

### **Phase 3: Advanced**
- 🔲 Background sync service
- 🔲 Conflict resolution
- 🔲 Data encryption
- 🔲 Performance optimization

## 📖 References

- [Floor Documentation](https://pub.dev/packages/floor)
- [SQLite Documentation](https://www.sqlite.org/docs.html)
- [Architecture Guide](./ARCHITECTURE_GUIDE.md)
- [Project Status](./PROJECT_STATUS.md)
- [Systems Guide](./SYSTEMS_GUIDE.md)
