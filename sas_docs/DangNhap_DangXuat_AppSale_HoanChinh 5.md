## 1. <PERSON><PERSON><PERSON> chức năng: <PERSON><PERSON><PERSON> nhập / Đăng xuất hệ thống App Sale

### 2. <PERSON><PERSON><PERSON> đích

Cho phép người dùng truy cập vào hệ thống App Sale bằng tài khoản nội bộ ngân hàng (email Outlook) để thực hiện nghiệp vụ theo vai trò. Hỗ trợ cả phương thức đăng nhập bằng mật khẩu và đăng nhập sinh trắc học.

### 3. Vai trò sử dụng

- Cộng tác viên (CTV)
- <PERSON><PERSON> bộ bán hà<PERSON> (CBBH – RM/SRM)
- GDV/HTTD
- C<PERSON><PERSON> quản lý (GĐ/PGĐ Chi nhánh, GĐ Khu vực)
- Admin

### 4. Chức năng và luồng xử lý

#### Sơ đồ tổng quan (BPMN dạng mô tả)

**Bắt đầu** → [Mở App Sale] → [<PERSON><PERSON><PERSON> phương thức đăng nhập]

**Nhánh 1: <PERSON><PERSON><PERSON> nhập bằng mật khẩu**

- <PERSON>h<PERSON><PERSON> Email + Mật khẩu
- Kiểm tra: root/jailbreak → Nếu có: Thông báo lỗi → Kết thúc
- Kiểm tra kết nối mạng → Nếu lỗi: Cảnh báo → Kết thúc
- Kiểm tra trạng thái khóa đăng nhập → Nếu bị khóa: Cảnh báo thời gian còn lại → Kết thúc
- Kiểm tra tài khoản/mật khẩu → Nếu sai: Thông báo lỗi → Kết thúc
- Nếu đúng -> CTV đăng nhập thành công →  Gợi ý bật sinh trắc học (nếu chưa có) → Vào hệ thống

**Nhánh 2: Đăng nhập bằng sinh trắc học**

- Click icon sinh trắc học
- Xác thực: Face ID/Vân tay
- Nếu đúng → Vào hệ thống
- Nếu chưa thiết lập -> Hiển thị thông báo hãy thiết lập STH khi đăng nhập thành công
- Nếu sai:
  - Cho Thử lại → Nếu sai 3 lần → Gợi ý dùng mật khẩu
  - Nếu hủy → Quay lại trang đăng nhập

**Đăng xuất:**

- Người dùng chủ động chọn "Đăng xuất"
- Hệ thống ghi nhớ email, xoá token, kết thúc phiên

→ **Kết thúc**

#### 4.1. Đăng nhập bằng mật khẩu

- Người dùng mở App Sale và nhập:
  - Email KLB (Outlook)
  - Mật khẩu (là mật khẩu mail KLB)
- Hệ thống kiểm tra:
  - Thiết bị có root/jailbreak không → Nếu có thì chặn
  - Kết nối Internet có sẵn không → Nếu không thì cảnh báo
  - Thiết bị có bị khóa đăng nhập tạm thời không
  - User có tồn tại và mật khẩu đúng không
- Nếu hợp lệ → Cho phép truy cập hệ thống
- Nếu người dùng đăng nhập trên thiết bị mới → Đăng xuất thiết bị cũ và thông báo

#### 4.2. Thiết lập và đăng nhập sinh trắc học (Face ID/Vân tay)

- Sau khi đăng nhập thành công lần đầu:
  - Hệ thống gợi ý thiết lập sinh trắc học qua popup
  - Người dùng có thể chọn “Đồng ý” hoặc “Nhắc lại sau”
- Người dùng cũng có thể vào menu cá nhân để bật/tắt sinh trắc học chủ động
- Sau khi thiết lập:
  - Lần đăng nhập sau, hệ thống sẽ tự động xác thực vân tay/khuôn mặt
  - Nếu xác thực thất bại: Cho phép Thử lại / Huỷ / Chuyển sang nhập mật khẩu

#### 4.3. Đăng xuất

- Người dùng chọn "Đăng xuất" từ menu cá nhân
- Hệ thống kết thúc phiên làm việc, ghi nhớ email cho lần sau nhưng không lưu mật khẩu
- Sau 8 tiếng không thao tác, hệ thống tự đăng xuất (auto logout)

### 5. Quy tắc nghiệp vụ

(Các mã BR được tham chiếu trực tiếp trong phần mô tả chức năng ở mục 4 và 5 để dễ dàng kiểm tra logic và đối chiếu nghiệp vụ trong tài liệu URD tổng thể.)

| Mã    | Quy tắc                                                           |
| ----- | ----------------------------------------------------------------- |
| BR001 | Chỉ cho phép 1 thiết bị đăng nhập tại 1 thời điểm                 |
| BR002 | Đăng nhập bằng email Outlook nội bộ KienlongBank                  |
| BR003 | Mật khẩu: 6–20 ký tự, bắt buộc có chữ/số/ký tự đặc biệt           |
| BR004 | Gợi ý bật sinh trắc học sau khi đăng nhập thành công              |
| BR005 | Nếu sinh trắc học sai 3 lần liên tiếp → quay lại trang chủ        |
| BR006 | Sau đăng xuất: xóa toàn bộ phiên, không lưu mật khẩu              |
| BR007 | Tự động đăng xuất sau 8 tiếng không hoạt động                     |
| BR008 | CTV đăng nhập lần đầu bắt buộc điền đầy đủ thông tin map hợp đồng |
| BR009 | Cho phép thiết lập sinh trắc học tại màn hình avatar cá nhân      |

### 6. Xử lý ngoại lệ

| Mã lỗi | Mô tả                         | Hành động                                                |
| ------ | ----------------------------- | -------------------------------------------------------- |
| EX001  | Thiết bị bị root/jailbreak    | Chặn đăng nhập, thông báo bảo mật                        |
| EX002  | Không có kết nối mạng         | Hiển thị cảnh báo kiểm tra mạng                          |
| EX003  | Thiết bị đang bị khóa tạm     | Thông báo thời gian khóa, chờ hết mới cho phép đăng nhập |
| EX004  | Sai thông tin đăng nhập       | Giới hạn số lần, thông báo lỗi rõ ràng                   |
| EX005  | Đăng nhập trên thiết bị mới   | Đăng xuất thiết bị cũ, thông báo popup                   |
| EX006  | Sinh trắc học sai             | Cho phép Thử lại / Huỷ / Chuyển sang nhập mật khẩu       |
| EX007  | Thông tin map CTV không khớp  | Cảnh báo tại trường lỗi, không cho lưu                   |
| EX008  | Không cấp quyền sinh trắc học | Yêu cầu người dùng mở cài đặt thiết bị để cấp quyền      |

### 7. Ghi chú UI/UX

#### Màn hình đăng nhập

- Nền gradient xanh dương, logo KienlongBank hiển thị phía trên
- Các thành phần:
  - **Textbox Email**: Tự động điền nếu đã đăng nhập trước đó
  - **Textbox Mật khẩu**: Có biểu tượng mắt để hiện/ẩn
  - **Button "Đăng nhập"**: Nút lớn, màu xanh dương nổi bật
  - **Icon sinh trắc học**: Hiển thị nếu đã thiết lập (vân tay hoặc Face ID)
  - **Link "Đăng ký tài khoản"**: Dẫn đến màn hình đăng ký cho CTV

#### Màn hình avatar cá nhân

- Hiển thị trong menu hoặc trang hồ sơ
- Có mục "Thiết lập đăng nhập sinh trắc học" dạng toggle switch
- Hiển thị mô tả: “Đăng nhập nhanh bằng Face ID/Vân tay”

#### Màn hình thiết lập sinh trắc học

- Hiển thị popup khi đăng nhập thành công lần đầu (nếu chưa thiết lập)
- Gợi ý bật sinh trắc học: “Sử dụng sinh trắc học để tiết kiệm thời gian đăng nhập?”
- Nút: Đồng ý | Nhắc sau

#### Lưu ý giao diện

- Thiết kế hiện đại, bo góc, tone màu xanh chủ đạo của KienlongBank
- Giao diện tối giản, dễ thao tác cho người dùng phổ thông