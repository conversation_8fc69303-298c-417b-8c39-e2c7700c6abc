import 'package:flutter_test/flutter_test.dart';
import 'package:sales_app/presentation/controllers/network_state.dart';

void main() {
  group('Network Monitoring System Tests', () {
    test('NetworkState should have correct properties', () {
      // Test connected state
      const connected = NetworkState.connected();
      expect(connected.isConnected, true);
      expect(connected.isDisconnected, false);
      expect(connected.isUnstable, false);
      expect(connected.isChecking, false);
      expect(connected.statusMessage, 'Đã kết nối');
      expect(connected.statusIcon, '✅');

      // Test disconnected state
      const disconnected = NetworkState.disconnected();
      expect(disconnected.isConnected, false);
      expect(disconnected.isDisconnected, true);
      expect(disconnected.isUnstable, false);
      expect(disconnected.isChecking, false);
      expect(disconnected.statusMessage, 'Mất kết nối mạng');
      expect(disconnected.statusIcon, '❌');

      // Test unstable state
      const unstable = NetworkState.unstable();
      expect(unstable.isConnected, false);
      expect(unstable.isDisconnected, false);
      expect(unstable.isUnstable, true);
      expect(unstable.isChecking, false);
      expect(unstable.statusMessage, '<PERSON><PERSON><PERSON> nối không ổn định');
      expect(unstable.statusIcon, '⚠️');

      // Test checking state
      const checking = NetworkState.checking();
      expect(checking.isConnected, false);
      expect(checking.isDisconnected, false);
      expect(checking.isUnstable, false);
      expect(checking.isChecking, true);
      expect(checking.statusMessage, 'Đang kiểm tra kết nối...');
      expect(checking.statusIcon, '🔄');
    });

    test('NetworkState equality should work correctly', () {
      const state1 = NetworkState.connected();
      const state2 = NetworkState.connected();
      const state3 = NetworkState.disconnected();

      expect(state1, equals(state2));
      expect(state1, isNot(equals(state3)));
    });

    test('NetworkState when method should work correctly', () {
      const connected = NetworkState.connected();
      const disconnected = NetworkState.disconnected();
      const unstable = NetworkState.unstable();
      const checking = NetworkState.checking();

      // Test when method for connected
      final connectedResult = connected.when(
        connected: () => 'connected',
        disconnected: () => 'disconnected',
        unstable: () => 'unstable',
        checking: () => 'checking',
      );
      expect(connectedResult, 'connected');

      // Test when method for disconnected
      final disconnectedResult = disconnected.when(
        connected: () => 'connected',
        disconnected: () => 'disconnected',
        unstable: () => 'unstable',
        checking: () => 'checking',
      );
      expect(disconnectedResult, 'disconnected');

      // Test when method for unstable
      final unstableResult = unstable.when(
        connected: () => 'connected',
        disconnected: () => 'disconnected',
        unstable: () => 'unstable',
        checking: () => 'checking',
      );
      expect(unstableResult, 'unstable');

      // Test when method for checking
      final checkingResult = checking.when(
        connected: () => 'connected',
        disconnected: () => 'disconnected',
        unstable: () => 'unstable',
        checking: () => 'checking',
      );
      expect(checkingResult, 'checking');
    });
  });
}
