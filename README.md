# 🏦 KienlongBank Sales App

Ứng dụng di động hỗ trợ đội ngũ bán hàng và cộng tác viên (CTV) của KienlongBank trong việc quản lý khách hàng, sản phẩm vay và các hoạt động bán hàng.

## 📋 Mục lục

- [🏗️ Kiến trúc tổng thể](#️-kiến-trúc-tổng-thể)
- [📁 Cấu trúc thư mục](#-cấu-trúc-thư-mục)
- [🛠️ Công nghệ và thư viện](#️-công-nghệ-và-thư-viện)
- [🎯 Tính năng chính](#-tính-năng-chính)
- [🚀 Cài đặt và chạy ứng dụng](#-cài-đặt-và-chạy-ứng-dụng)
- [📖 Tài liệu kỹ thuật](#-tài-liệu-kỹ-thuật)

## 🏗️ Kiến trúc tổng thể

### Clean Architecture
Ứng dụng được xây dựng theo mô hình **Clean Architecture** với 3 layer chính:

```
┌─────────────────────────────────────────┐
│              Presentation               │
│  (UI, Widgets, Screens, State Management)│
├─────────────────────────────────────────┤
│                Domain                   │
│    (Entities, Repositories, Services)   │
├─────────────────────────────────────────┤
│                 Data                    │
│  (Models, DataSources, Implementations) │
└─────────────────────────────────────────┘
```

### State Management
- **Riverpod + Hooks**: Quản lý state reactive và hiệu quả
- **Flutter Hooks**: Tối ưu hóa lifecycle và state local

### Navigation System
- **Go Router**: Declarative routing với type-safe navigation
- **Clean Architecture**: Router tuân thủ Clean Architecture principles
- **Navigation Extensions**: Type-safe navigation methods với auto-completion

### Dependency Injection
- **GetIt + Injectable**: Quản lý dependencies tự động
- **Module pattern**: Tổ chức dependencies theo module

## 📁 Cấu trúc thư mục

```
lib/
├── core/                    # Các thành phần core của ứng dụng
│   ├── constants/          # Hằng số (API, dimensions, storage keys)
│   ├── enums/             # Enum definitions
│   ├── error/             # Error handling và failures
│   ├── network/           # Network utilities
│   ├── router/            # Navigation constants & patterns (Clean Architecture)
│   │   ├── app_routes.dart # Route constants và validation
│   │   └── navigation_patterns.md # Navigation patterns documentation
│   ├── services/          # Core services
│   ├── theme/             # Theme và styling
│   └── utils/             # Utilities (logger, responsive)
├── data/                   # Data layer
│   ├── datasources/       # Remote/Local data sources
│   │   ├── local/         # Local database (Floor ORM)
│   │   │   ├── dao/       # Data Access Objects
│   │   │   ├── database/  # Database configuration
│   │   │   └── user_local_datasource.dart # Local data source interface
│   │   └── remote/        # Remote API data sources
│   ├── models/            # Data models (JSON serialization)
│   │   ├── local/         # Local database models
│   │   └── user_model.dart # API response models
│   ├── network/           # Network layer (API clients, interceptors, retry, token refresh)
│   │   ├── api_client.dart # Manages multiple Dio instances
│   │   ├── base_api_service.dart # Base class for manual APIs
│   │   ├── dio_builder.dart # Factory for configured Dio instances
│   │   ├── openapi_client.dart # Wrapper for future generated APIs
│   │   └── interceptors/  # Auth, token refresh, logging, error, retry, mock interceptors
│   ├── repositories_impl/ # Repository implementations
│   └── services_impl/     # Service implementations
├── di/                     # Dependency Injection
│   ├── injection.dart     # DI configuration
│   ├── injection.config.dart # Generated DI config
│   └── module.dart        # DI modules
├── domain/                 # Domain layer
│   ├── entities/          # Business entities
│   ├── models/            # Domain models
│   ├── repositories/      # Repository interfaces
│   └── services/          # Service interfaces
├── generated/              # Generated files
│   ├── intl/             # Internationalization
│   └── l10n.dart         # Localization
├── l10n/                   # Localization files
│   ├── intl_en.arb       # English translations
│   └── intl_vi.arb       # Vietnamese translations
├── presentation/           # Presentation layer (Hybrid Structure)
│   ├── app.dart          # App configuration
│   ├── controllers/      # Riverpod controllers (state management)
│   │   ├── auth_controller.dart # Authentication controller
│   │   ├── auth_state.dart # Authentication state
│   │   └── *.g.dart      # Generated files
│   ├── router/           # Navigation system (Clean Architecture compliant)
│   │   ├── app_router.dart # Router configuration với Go Router
│   │   ├── navigation_extensions.dart # Type-safe navigation methods
│   │   └── router.dart   # Router exports
│   ├── screens/          # Screen widgets with co-located widgets
│   │   ├── auth/         # Authentication screens
│   │   │   └── widgets/  # Auth-specific widgets (co-located)
│   │   ├── demo/         # Demo screens (navigation testing)
│   │   ├── document/     # Document management
│   │   │   └── widgets/  # Document-specific widgets (co-located)
│   │   ├── home/         # Home screen
│   │   │   └── widgets/  # Home-specific widgets (co-located)
│   │   └── identity/     # Identity verification
│   │       └── widgets/  # Identity-specific widgets (co-located)
│   └── widgets/          # Truly reusable widgets only
│       ├── common/       # Common widgets (used across features)
│       ├── loading/      # Adaptive loading system
│       │   ├── adaptive_loading_overlay.dart # Core overlay widget
│       │   ├── loading_mixin.dart # Mixin & base classes
│       │   └── examples/ # Usage examples
│       └── shared/       # Cross-feature widgets
└── main.dart              # Entry point
```

### Thư mục khác
```
android/                   # Android platform code
ios/                       # iOS platform code
web/                       # Web platform code
linux/                     # Linux platform code
macos/                     # macOS platform code
windows/                   # Windows platform code
assets/                    # Static assets
├── images/               # Image assets
docs/                      # Documentation
sas_docs/                  # Business requirements documents
test/                      # Unit tests
```
## 🛠️ Công nghệ và thư viện

### Framework & Platform
- **Flutter**: `>=3.10.0` - Cross-platform mobile framework
- **Dart**: `>=3.0.0 <4.0.0` - Programming language

### State Management & Architecture
| Thư viện | Version | Mục đích |
|----------|---------|----------|
| `hooks_riverpod` | ^2.5.1 | State management reactive |
| `flutter_hooks` | ^0.21.2 | Lifecycle và state hooks |
| `riverpod_annotation` | ^2.3.5 | Code generation cho Riverpod |
| `riverpod_generator` | ^2.4.0 | Generator cho Riverpod providers |

### Navigation & Routing
| Thư viện | Version | Mục đích |
|----------|---------|----------|
| `go_router` | ^15.1.2 | Declarative routing system |

### Dependency Injection
| Thư viện | Version | Mục đích |
|----------|---------|----------|
| `get_it` | ^7.6.7 | Service locator |
| `injectable` | ^2.4.1 | DI annotations |
| `injectable_generator` | ^2.4.1 | Code generation cho DI |

### Networking & Data
| Thư viện | Version | Mục đích |
|----------|---------|----------|
| `dio` | ^5.4.1 | HTTP client |
| `connectivity_plus` | ^5.0.2 | Network connectivity check |
| `dartz` | ^0.10.1 | Functional programming (Either, Option) |

### Data Serialization
| Thư viện | Version | Mục đích |
|----------|---------|----------|
| `freezed` | ^2.4.7 | Immutable classes & unions |
| `freezed_annotation` | ^2.4.1 | Annotations cho Freezed |
| `json_annotation` | ^4.8.1 | JSON serialization annotations |
| `json_serializable` | ^6.7.1 | JSON serialization generator |

### Storage & Security
| Thư viện | Version | Mục đích |
|----------|---------|----------|
| `shared_preferences` | 2.2.0 | Local storage đơn giản |
| `flutter_secure_storage` | ^9.0.0 | Secure storage cho tokens |

### Local Database & Offline Support
| Thư viện | Version | Mục đích |
|----------|---------|----------|
| `floor` | ^1.4.2 | SQLite ORM cho Flutter |
| `sqflite` | ^2.3.0 | SQLite database engine |
| `path` | ^1.8.3 | File path utilities |

### UI & UX
| Thư viện | Version | Mục đích |
|----------|---------|----------|
| `flutter_screenutil` | ^5.9.0 | Responsive design |
| `cupertino_icons` | ^1.0.6 | iOS-style icons |

### Media & Camera
| Thư viện | Version | Mục đích |
|----------|---------|----------|
| `image_picker` | ^1.0.4 | Chọn ảnh từ gallery/camera |
| `camera` | ^0.11.0+2 | Camera functionality |

### Internationalization
| Thư viện | Version | Mục đích |
|----------|---------|----------|
| `flutter_localizations` | SDK | Flutter localization support |
| `intl` | ^0.19.0 | Internationalization utilities |
| `intl_utils` | ^2.8.7 | Localization code generation |

### Development & Debugging
| Thư viện | Version | Mục đích |
|----------|---------|----------|
| `logger` | ^2.0.2+1 | Logging system |
| `flutter_lints` | ^3.0.1 | Dart/Flutter linting rules |
| `build_runner` | ^2.4.8 | Code generation runner |
| `analyzer` | ^6.4.1 | Dart code analysis |
| `floor_generator` | ^1.4.2 | Code generation cho Floor ORM |

## 🎯 Tính năng chính

### 🔐 Authentication & Authorization ✅ **COMPLETED**
- **Đăng nhập**: Email/Password và sinh trắc học
- **Offline Login**: Đăng nhập với cached credentials khi không có mạng
- **Token Refresh System**: Automatic proactive (30s buffer) + reactive (401 error) token refresh
- **Thread-Safe Authentication**: Concurrent request handling với mutex pattern
- **Phân quyền**: CTV, CBBH, GDV/HTTD, Quản lý, Admin
- **Bảo mật**: Token-based authentication với secure storage và automatic refresh

### 🏠 Trang chủ (Homepage)
- **Thông tin CTV**: Hạn mức, dư nợ, tài sản bảo đảm
- **Thao tác nhanh**: Tìm kiếm, tạo khoản vay, thông báo
- **Dashboard**: Thống kê và báo cáo cá nhân

### 💰 Quản lý vay trả góp
- **Danh sách khoản vay**: Theo trạng thái và khách hàng
- **Tạo khoản vay mới**: Workflow hoàn chỉnh
- **Theo dõi**: Tình trạng duyệt và giải ngân

### 👥 Quản lý CTV
- **Đăng ký CTV mới**: Xác minh giấy tờ tự động
- **Thông tin CTV**: Hạn mức, hiệu suất, khách hàng
- **Phân cấp**: Tổ trưởng, trưởng nhóm

### 📄 Quản lý tài liệu
- **Chụp ảnh**: CCCD, giấy tờ tài sản
- **Xử lý ảnh**: Crop, blur detection, quality check
- **Upload**: Đồng bộ với hệ thống backend

### 💳 Thu tiền khách hàng
- **Danh sách**: Khách hàng cần thu tiền
- **Lịch sử**: Giao dịch và thanh toán
- **Thông báo**: Nhắc nhở và cảnh báo

### 📊 Báo cáo & Thống kê
- **Báo cáo cá nhân**: Hiệu suất bán hàng
- **Báo cáo nhóm**: Thống kê theo team
- **Export**: Xuất báo cáo PDF/Excel

### 🔔 Thông báo & Tin tức
- **Push notification**: Thông báo real-time
- **Tin tức**: Cập nhật chính sách, sản phẩm
- **Hướng dẫn**: Tutorial và FAQ

### 🌍 Environment Configuration ✅ **COMPLETED**
- **Multi-Environment Support**: Dev/Staging/Prod với dropdown selector
- **Clean Architecture**: Domain/Data layer separation tuân thủ Clean Architecture
- **Performance Optimized**: Static map configuration cho O(1) lookup
- **Reactive UI**: Real-time updates với StreamBuilder
- **Persistent Selection**: Environment choice được lưu across app restarts
- **Centralized Configuration**: URLs và settings quản lý tập trung

### 💾 Offline Data & Sync
- **Local Database**: SQLite với Floor ORM cho offline storage
- **Offline-First**: App hoạt động khi không có mạng
- **Auto Sync**: Tự động đồng bộ khi có kết nối trở lại
- **Conflict Resolution**: Xử lý xung đột dữ liệu
- **Graceful Fallback**: API failure → offline mode seamlessly
## 🚀 Cài đặt và chạy ứng dụng

### Yêu cầu hệ thống
- **Flutter**: >= 3.10.0
- **Dart**: >= 3.0.0
- **Android Studio** hoặc **VS Code**
- **Android SDK**: API level 21+ (Android 5.0+)
- **iOS**: iOS 12.0+ (nếu build cho iOS)

### Cài đặt dependencies
```bash
# Clone repository
git clone <repository-url>
cd sales_app

# Cài đặt dependencies
flutter pub get

# Generate code (Freezed, Injectable, Floor, etc.)
flutter packages pub run build_runner build --delete-conflicting-outputs

# Generate localization
flutter gen-l10n
```

### Chạy ứng dụng
```bash
# Debug mode
flutter run

# Release mode
flutter run --release

# Chạy trên device cụ thể
flutter run -d <device-id>

# Xem danh sách devices
flutter devices
```

### Build ứng dụng
```bash
# Build APK (Android)
flutter build apk --release

# Build App Bundle (Android)
flutter build appbundle --release

# Build iOS (macOS only)
flutter build ios --release
```

## 📖 Tài liệu kỹ thuật

### 📚 **Documentation Structure**

| File | Description | Target Audience |
|------|-------------|-----------------|
| **README.md** | Project overview, quick start | All developers |
| **[docs/ARCHITECTURE_GUIDE.md](docs/ARCHITECTURE_GUIDE.md)** | Architecture details, coding standards | All developers |
| **[docs/PROJECT_STATUS.md](docs/PROJECT_STATUS.md)** | Implementation status, roadmap | Project managers, leads |
| **[docs/SYSTEMS_GUIDE.md](docs/SYSTEMS_GUIDE.md)** | All systems: Environment, Navigation, Logging, Loading, Mock APIs | Developers working with systems |
| **[docs/NETWORK_GUIDE.md](docs/NETWORK_GUIDE.md)** | Complete network layer guide (API clients, token refresh, retry, mock, error handling) | Developers working with APIs |
| **[docs/TOKEN_REFRESH_GUIDE.md](docs/TOKEN_REFRESH_GUIDE.md)** | Complete token refresh system guide (proactive/reactive refresh, thread safety, mock support) | Developers working with authentication |
| **[docs/TOKEN_REFRESH_EXAMPLES.md](docs/TOKEN_REFRESH_EXAMPLES.md)** | Practical examples of 30s buffer mechanism and refresh scenarios | Developers implementing token refresh |
| **[docs/ERROR_HANDLING_GUIDE.md](docs/ERROR_HANDLING_GUIDE.md)** | Comprehensive error handling with retry integration | Developers, QA Engineers |
| **[docs/ENVIRONMENT_CONFIGURATION.md](docs/ENVIRONMENT_CONFIGURATION.md)** | Complete environment system guide (implementation, architecture, status) | Developers, DevOps, Architects |

### Code Generation
Ứng dụng sử dụng nhiều code generation tools:

```bash
# Generate tất cả (Freezed, Injectable, Floor, Riverpod)
flutter packages pub run build_runner build --delete-conflicting-outputs

# Watch mode (tự động generate khi file thay đổi)
flutter packages pub run build_runner watch --delete-conflicting-outputs

# Generate localization
flutter gen-l10n
```

### Key Systems

#### **Token Refresh System** ✅ **COMPLETED**
```dart
// Automatic token refresh - no code changes needed
final result = await userApiService.getProfile();
// Token automatically refreshed if needed (30s buffer + 401 handling)

// Manual token expiry check (optional)
final isExpired = await storageService.isTokenExpired();
if (isExpired) {
  // Will be handled automatically by TokenRefreshInterceptor
}

// Token refresh happens automatically in two ways:
// 1. Proactive: Before each request if token expires within 30s
// 2. Reactive: On 401 errors with automatic request retry
```

#### **Environment Configuration System** ✅ **COMPLETED**
```dart
// Get current environment
final environmentService = getIt<EnvironmentService>();
final currentEnv = environmentService.currentEnvironment;

// Switch environment
await environmentService.switchEnvironment(Environment.staging);

// Listen to environment changes
environmentService.environmentChanges.listen((config) {
  print('Environment: ${config.environment.displayName}');
  print('Base URL: ${config.baseUrl}');
});

// Check environment properties
if (environmentService.isDevelopment) {
  // Development-specific logic
}
```

#### **Token Refresh System** ✅ **COMPLETED**
```dart
// Automatic token refresh - completely transparent
final userProfile = await userApiService.getProfile();
final products = await productApiService.getProducts();
// Both calls automatically handle token refresh if needed

// System features:
// ✅ Proactive refresh: 30s before expiry
// ✅ Reactive refresh: On 401 errors with retry
// ✅ Thread-safe: Multiple concurrent requests handled safely
// ✅ Server-driven: Uses expires_in from API response
// ✅ Mock support: Complete mock refresh implementation

// Manual check (rarely needed)
final isExpired = await storageService.isTokenExpired();
// Returns true if token expires within 30 seconds
```

#### **Navigation System**
```dart
// Type-safe navigation với Go Router
context.goToSelectAccountType();    // Push navigation
context.replaceWithHome();          // Replace navigation
context.goBack();                   // Smart back with fallback
```

#### **Logging System**
```dart
import 'package:sales_app/core/utils/app_logger.dart';

// Log thông tin
AppLogger.info('User logged in successfully');

// Log với data
AppLogger.event('Button clicked', data: {
  'button': 'login',
  'timestamp': DateTime.now().toIso8601String(),
});

// Log lỗi
AppLogger.error('API call failed', error: exception);
```

#### **Loading System**
```dart
// Adaptive loading với BaseLoadingHookWidget
class MyScreen extends BaseLoadingHookWidget {
  @override
  bool isLoading(WidgetRef ref) => ref.watch(myProvider).isLoading;

  @override
  String? getLoadingMessage(WidgetRef ref) => 'Processing...'; // Optional

  @override
  Widget buildContent(BuildContext context, WidgetRef ref) {
    return Scaffold(body: MyContent());
  }
}
```

#### **Mock API System**
```dart
// Switch between mock and real APIs
AppConstants.isMockMode = true;  // Use mock APIs
AppConstants.isMockMode = false; // Use real APIs

// Test with mock users
await authController.login(
  email: '<EMAIL>',
  password: '123456',
);
```

#### **Offline Data System**
```dart
// Offline login with cached credentials
final result = await authRepository.login(
  email: '<EMAIL>',
  password: 'password123',
);
// Automatically falls back to offline mode if API fails

// Database operations
await databaseService.saveUserOffline(user);
final users = await databaseService.getAllUsers();
await databaseService.markUserAsSynced(userId, serverTimestamp);
```

### Dependency Injection
```dart
// Đăng ký service
@Injectable(as: AuthRepository)
class AuthRepositoryImpl implements AuthRepository {
  // Implementation
}

// Sử dụng service
final authRepo = getIt<AuthRepository>();
```

### State Management với Riverpod
```dart
// Provider
@riverpod
class AuthNotifier extends _$AuthNotifier {
  @override
  AuthState build() => const AuthState.initial();

  Future<void> login(String email, String password) async {
    // Implementation
  }
}

// Consumer trong Widget
class LoginScreen extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authState = ref.watch(authNotifierProvider);
    // UI implementation
  }
}
```

### Architecture & Development
Xem chi tiết tại: [docs/ARCHITECTURE_GUIDE.md](docs/ARCHITECTURE_GUIDE.md)

```dart
// Type-safe navigation
import 'package:sales_app/presentation/router/navigation_extensions.dart';

// Push navigation (có back button)
context.goToSelectAccountType();
context.goToCtvPolicy();
context.goToKycIdGuide();

// Replace navigation (không có back button)
context.replaceWithHome();  // Sau khi login
context.replaceWithLogin(); // Khi logout

// Utility navigation
context.goBack();
context.pushToHome();
```

#### Navigation Patterns
```dart
// Registration Flow (Push pattern)
Login → SelectAccount → CtvPolicy → KycGuide → Identity → PersonalInfo → Success
        [Back]         [Back]      [Back]     [Back]     [Back]

// Login Flow (Replace pattern)
Login → Home (no back button - correct!)

// Document Flow (Push pattern)
Home → CaptureDocument → PreviewDocument
      [Back]            [Back]
```

## 🗂️ Tài liệu nghiệp vụ

### Business Requirements
- [Đăng nhập/Đăng xuất](sas_docs/DangNhap_DangXuat_AppSale_HoanChinh%205.md)
- [Đăng ký CTV](sas_docs/SRS_DangKyCTV_AppSale%205.md)
- [Quản lý thông tin CTV](sas_docs/SRS_QuanLy_ThongTin_CTV_AppSale%202.md)

### API Integration
- **Base URL**: Cấu hình trong `lib/core/constants/api_constants.dart`
- **Authentication**: Bearer token với automatic refresh mechanism (proactive + reactive)
- **Token Refresh**: Automatic 30s buffer + 401 error handling với thread-safe concurrent requests
- **Error Handling**: Centralized error handling với `Either<Failure, Success>`
- **Mock API Support**: Complete mock implementation including refresh token endpoints

### Security
- **Token Storage**: Flutter Secure Storage với encryption và automatic expiry tracking
- **Token Refresh**: Server-driven expiry time với 30-second buffer for security
- **Thread-Safe Authentication**: Mutex pattern prevents concurrent refresh conflicts
- **Automatic Logout**: On refresh token failure để maintain security
- **Biometric Auth**: Hỗ trợ vân tay và Face ID
- **Network Security**: Certificate pinning (nếu cần)

## 🧪 Testing

### Unit Tests
```bash
# Chạy tất cả tests
flutter test

# Chạy test với coverage
flutter test --coverage

# Xem coverage report
genhtml coverage/lcov.info -o coverage/html
open coverage/html/index.html
```

### Widget Tests
```dart
testWidgets('Login screen should display correctly', (tester) async {
  await tester.pumpWidget(const MyApp());
  expect(find.text('Đăng nhập'), findsOneWidget);
});
```

## 🔧 Development Guidelines

### Code Style
- Tuân thủ [Dart Style Guide](https://dart.dev/guides/language/effective-dart/style)
- Sử dụng `flutter_lints` để kiểm tra code quality
- Format code với `dart format`

### Git Workflow
```bash
# Feature branch
git checkout -b feature/new-feature
git commit -m "feat: add new feature"
git push origin feature/new-feature

# Commit message format
# feat: new feature
# fix: bug fix
# docs: documentation
# style: formatting
# refactor: code refactoring
# test: adding tests
```

### Performance
- Sử dụng `flutter_screenutil` cho responsive design
- Optimize images trong `assets/images/`
- Lazy loading cho danh sách dài
- Memory management với proper dispose

## 📞 Hỗ trợ

### Troubleshooting
1. **Build errors**: Chạy `flutter clean && flutter pub get`
2. **Code generation issues**: Chạy build_runner với `--delete-conflicting-outputs`
3. **Localization not working**: Chạy `flutter gen-l10n`

### Liên hệ
- **Team Lead**: [Tên team lead]
- **Technical Lead**: [Tên technical lead]
- **Project Manager**: [Tên PM]

---

**© 2024 KienlongBank. All rights reserved.**
