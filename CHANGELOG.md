# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

### Added
- 🧭 **Navigation System với Go Router**
  - Declarative routing system với type-safe navigation
  - Clean Architecture compliant router implementation
  - Navigation extensions với auto-completion
  - Proper back button behavior (push vs replace patterns)
  - Centralized route management
  - Error handling với custom error pages
  - Navigation demo screen cho testing
  - Comprehensive navigation logging

### Changed
- 🏗️ **Router Architecture Refactoring**
  - Di chuyển router từ Core layer sang Presentation layer
  - Tuân thủ Clean Architecture principles
  - Tách biệt route constants (Core) và router configuration (Presentation)
  - Cập nhật tất cả navigation imports trong project

### Technical Details
- **Dependencies Added:**
  - `go_router: ^15.1.2` - Declarative routing system

- **Files Added:**
  - `lib/presentation/router/app_router.dart` - Router configuration
  - `lib/presentation/router/navigation_extensions.dart` - Type-safe navigation methods
  - `lib/presentation/router/router.dart` - Router exports
  - `lib/presentation/router/CLEAN_ARCHITECTURE.md` - Architecture documentation
  - `lib/presentation/screens/demo/navigation_demo.dart` - Navigation testing screen
  - `docs/NAVIGATION.md` - Navigation system documentation

- **Files Modified:**
  - `lib/main.dart` - Updated router import path
  - `lib/presentation/screens/auth/login_screen.dart` - Updated navigation imports
  - `lib/presentation/screens/home/<USER>
  - `lib/presentation/screens/identity/identity_upload_page.dart` - Updated navigation imports
  - `lib/presentation/screens/auth/personal_info_confirmation_screen.dart` - Updated navigation imports
  - `lib/presentation/screens/auth/cvt_policy_screen.dart` - Updated navigation imports
  - `lib/presentation/screens/auth/kyc_id_guide_screen.dart` - Updated navigation imports
  - `lib/presentation/screens/auth/widgets/account_type_selector.dart` - Updated navigation imports
  - `lib/presentation/screens/document/capture_document_screen.dart` - Updated navigation imports
  - `README.md` - Updated documentation với navigation system info
  - `docs/LOGGING.md` - Added navigation logging documentation

- **Files Removed:**
  - `lib/core/router/app_router.dart` - Moved to presentation layer
  - `lib/core/router/navigation_extensions.dart` - Moved to presentation layer
  - `lib/core/router/navigation_demo.dart` - Moved to presentation layer
  - `lib/core/router/router.dart` - Moved to presentation layer

### Navigation Patterns Implemented
- **Push Navigation (có back button):**
  - Registration flow: Login → SelectAccount → CtvPolicy → KycGuide → Identity → PersonalInfo → Success
  - Document flow: Home → CaptureDocument → PreviewDocument

- **Replace Navigation (không có back button):**
  - Login flow: Login → Home (after successful login)
  - Logout flow: Any screen → Login

### Benefits Achieved
- ✅ **Type-safe navigation** với compile-time validation
- ✅ **Clean Architecture compliance** - proper dependency direction
- ✅ **Improved UX** - correct back button behavior
- ✅ **Developer Experience** - auto-completion và IntelliSense
- ✅ **Maintainability** - centralized route management
- ✅ **Scalability** - easy to add new routes
- ✅ **Testing** - comprehensive navigation testing tools
- ✅ **Documentation** - detailed guides và examples

## [Previous Versions]

### [1.0.0] - Initial Release
- 🏦 KienlongBank Sales App initial implementation
- 🏗️ Clean Architecture setup
- 🎯 Core features implementation
- 📱 Multi-platform support (Android, iOS, Web)
- 🔐 Authentication system
- 👥 CTV management
- 💰 Loan management
- 📄 Document management
- 📊 Reporting system
- 🔔 Notification system
